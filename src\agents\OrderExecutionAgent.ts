import { EventEmitter } from 'events';
import { Order } from 'ccxt';
import { TradingState, AgentInput, AgentOutput, TradeEvent, Position, TradingConfig, TradingSystemError } from '@/types';
import { PhemexService } from '@/services/PhemexService';
import { logger, calculatePositionSize, formatCurrency } from '@/utils';
import { CONSTANTS } from '@/config';
import { v4 as uuidv4 } from 'uuid';

export class OrderExecutionAgent extends EventEmitter {
  private phemexService: PhemexService;
  private config: TradingConfig;
  private isActive: boolean = false;
  private lastExecutionTimestamp: number = 0;
  private executionHistory: TradeEvent[] = [];
  private pendingOrders: Map<string, Order> = new Map();

  constructor(phemexService: PhemexService, config: TradingConfig) {
    super();
    this.phemexService = phemexService;
    this.config = config;

    logger.info('OrderExecutionAgent initialized', {
      enableLiveTrading: this.config.enableLiveTrading,
      leverage: this.config.leverage,
    });
  }

  // Initialize the agent
  async initialize(): Promise<void> {
    try {
      this.isActive = true;
      this.emit('initialized');
      
      logger.info('OrderExecutionAgent started successfully');
    } catch (error) {
      logger.error('Failed to initialize OrderExecutionAgent:', error);
      throw new TradingSystemError(
        'Failed to initialize OrderExecutionAgent',
        'ORDER_EXECUTION_INIT_ERROR',
        'OrderExecutionAgent',
        error as Error
      );
    }
  }

  // Process agent input and execute trades
  async process(state: TradingState, input: AgentInput): Promise<{ state: TradingState; output: AgentOutput }> {
    try {
      const startTime = Date.now();
      let updated = false;
      let tradeEvents: TradeEvent[] = [];

      // Execute trade if we have a decision
      if (state.decision && this.shouldExecuteTrade(state.decision)) {
        const events = await this.executeTradeDecision(state);
        
        if (events.length > 0) {
          tradeEvents = events;
          state = this.updateTradeEventsInState(state, events);
          updated = true;
        }
      }

      // Check and update existing positions
      const positionUpdates = await this.updatePositions(state);
      if (positionUpdates.updated) {
        state = positionUpdates.state;
        tradeEvents.push(...positionUpdates.events);
        updated = true;
      }

      const processingTime = Date.now() - startTime;

      const output: AgentOutput = {
        success: true,
        data: {
          updated,
          processingTime,
          tradeEvents,
          activePositions: state.activePositions.length,
          pendingOrders: this.pendingOrders.size,
        },
        timestamp: Date.now(),
      };

      if (updated && tradeEvents.length > 0) {
        logger.info('OrderExecutionAgent executed trades', {
          eventCount: tradeEvents.length,
          events: tradeEvents.map(e => e.type),
        });

        // Emit trade events
        tradeEvents.forEach(event => {
          this.emit('trade_executed', event);
        });
      }

      return { state, output };
    } catch (error) {
      logger.error('OrderExecutionAgent processing error:', error);
      
      const output: AgentOutput = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      };

      return { state, output };
    }
  }

  // Determine if we should execute a trade
  private shouldExecuteTrade(decision: any): boolean {
    if (!this.config.enableLiveTrading) {
      logger.info('Live trading disabled, skipping execution');
      return false;
    }

    return !!(
      decision &&
      decision.action !== 'hold' &&
      decision.confidence >= CONSTANTS.MIN_TRADE_CONFIDENCE
    );
  }

  // Execute trade decision
  private async executeTradeDecision(state: TradingState): Promise<TradeEvent[]> {
    const events: TradeEvent[] = [];
    const decision = state.decision!;

    try {
      switch (decision.action) {
        case 'enter_long':
        case 'enter_short':
          const entryEvents = await this.executeEntry(decision, state);
          events.push(...entryEvents);
          break;

        case 'close_position':
          const closeEvents = await this.executeClose(decision, state);
          events.push(...closeEvents);
          break;

        case 'scale_out':
          const scaleEvents = await this.executeScaleOut(decision, state);
          events.push(...scaleEvents);
          break;

        default:
          logger.warn(`Unknown trade action: ${decision.action}`);
      }

      this.lastExecutionTimestamp = Date.now();
    } catch (error) {
      logger.error('Error executing trade decision:', error);
      
      // Create error event
      events.push({
        id: uuidv4(),
        type: 'position_opened', // Will be overridden
        positionId: '',
        symbol: decision.symbol,
        side: decision.side,
        amount: 0,
        price: 0,
        timestamp: Date.now(),
        message: `Execution failed: ${error}`,
      });
    }

    return events;
  }

  // Execute entry order
  private async executeEntry(decision: any, state: TradingState): Promise<TradeEvent[]> {
    const events: TradeEvent[] = [];

    try {
      // Get account balance for position sizing
      const balance = await this.phemexService.getBalance();
      const accountBalance = balance.total || 10000;

      // Calculate position size
      const currentPrice = state.orderBook?.bids[0]?.[0] || decision.price || 0;
      const stopLossPrice = decision.stopLoss || this.calculateStopLoss(currentPrice, decision.side);
      
      const positionSize = calculatePositionSize(
        accountBalance,
        this.config.riskPercentage,
        currentPrice,
        stopLossPrice,
        this.config.leverage
      );

      // Execute market order
      const order = await this.phemexService.createMarketOrder(
        decision.symbol,
        decision.side,
        positionSize
      );

      // Create position
      const position: Position = {
        id: uuidv4(),
        symbol: decision.symbol,
        side: decision.action === 'enter_long' ? 'long' : 'short',
        size: positionSize,
        entryPrice: order.price || currentPrice,
        currentPrice: currentPrice,
        unrealizedPnL: 0,
        realizedPnL: 0,
        leverage: this.config.leverage,
        stopLoss: decision.stopLoss,
        takeProfit: decision.takeProfit,
        openTimestamp: Date.now(),
        lastUpdateTimestamp: Date.now(),
        status: 'open',
      };

      // Create trade event
      const event: TradeEvent = {
        id: uuidv4(),
        type: 'position_opened',
        positionId: position.id,
        symbol: decision.symbol,
        side: decision.side,
        amount: positionSize,
        price: order.price || currentPrice,
        timestamp: Date.now(),
        message: `${decision.action} position opened at ${formatCurrency(order.price || currentPrice)}`,
      };

      events.push(event);
      this.executionHistory.push(event);

      // Set stop loss and take profit orders if specified
      if (decision.stopLoss) {
        await this.setStopLoss(position, decision.stopLoss);
      }

      if (decision.takeProfit) {
        await this.setTakeProfit(position, decision.takeProfit);
      }

      logger.info('Entry order executed successfully', {
        symbol: decision.symbol,
        side: decision.side,
        amount: positionSize,
        price: order.price,
      });

    } catch (error) {
      logger.error('Failed to execute entry order:', error);
      throw error;
    }

    return events;
  }

  // Execute close order
  private async executeClose(decision: any, state: TradingState): Promise<TradeEvent[]> {
    const events: TradeEvent[] = [];

    try {
      // Find position to close
      const position = state.activePositions.find(p => p.symbol === decision.symbol);
      
      if (!position) {
        logger.warn(`No position found to close for ${decision.symbol}`);
        return events;
      }

      // Execute market order to close position
      const closeSide = position.side === 'long' ? 'sell' : 'buy';
      const order = await this.phemexService.createMarketOrder(
        decision.symbol,
        closeSide,
        position.size
      );

      // Calculate P&L
      const exitPrice = order.price || decision.price || 0;
      const pnl = this.calculatePnL(position, exitPrice);

      // Create trade event
      const event: TradeEvent = {
        id: uuidv4(),
        type: 'position_closed',
        positionId: position.id,
        symbol: decision.symbol,
        side: closeSide,
        amount: position.size,
        price: exitPrice,
        pnl,
        timestamp: Date.now(),
        message: `Position closed at ${formatCurrency(exitPrice)} with P&L: ${formatCurrency(pnl)}`,
      };

      events.push(event);
      this.executionHistory.push(event);

      logger.info('Close order executed successfully', {
        symbol: decision.symbol,
        side: closeSide,
        amount: position.size,
        price: exitPrice,
        pnl,
      });

    } catch (error) {
      logger.error('Failed to execute close order:', error);
      throw error;
    }

    return events;
  }

  // Execute scale out order
  private async executeScaleOut(decision: any, state: TradingState): Promise<TradeEvent[]> {
    const events: TradeEvent[] = [];

    try {
      // Find position to scale out
      const position = state.activePositions.find(p => p.symbol === decision.symbol);
      
      if (!position) {
        logger.warn(`No position found to scale out for ${decision.symbol}`);
        return events;
      }

      // Scale out 50% by default
      const scaleAmount = position.size * CONSTANTS.PARTIAL_CLOSE_PERCENTAGE;
      const closeSide = position.side === 'long' ? 'sell' : 'buy';
      
      const order = await this.phemexService.createMarketOrder(
        decision.symbol,
        closeSide,
        scaleAmount
      );

      // Calculate partial P&L
      const exitPrice = order.price || decision.price || 0;
      const partialPnL = this.calculatePnL(
        { ...position, size: scaleAmount },
        exitPrice
      );

      // Create trade event
      const event: TradeEvent = {
        id: uuidv4(),
        type: 'partial_close',
        positionId: position.id,
        symbol: decision.symbol,
        side: closeSide,
        amount: scaleAmount,
        price: exitPrice,
        pnl: partialPnL,
        timestamp: Date.now(),
        message: `Partial close (50%) at ${formatCurrency(exitPrice)} with P&L: ${formatCurrency(partialPnL)}`,
      };

      events.push(event);
      this.executionHistory.push(event);

      logger.info('Scale out order executed successfully', {
        symbol: decision.symbol,
        amount: scaleAmount,
        price: exitPrice,
        pnl: partialPnL,
      });

    } catch (error) {
      logger.error('Failed to execute scale out order:', error);
      throw error;
    }

    return events;
  }

  // Update existing positions
  private async updatePositions(state: TradingState): Promise<{
    state: TradingState;
    events: TradeEvent[];
    updated: boolean;
  }> {
    const events: TradeEvent[] = [];
    let updated = false;

    try {
      // Get current positions from exchange
      const exchangePositions = await this.phemexService.getPositions(this.config.symbol);
      
      // Update position data
      const updatedPositions = state.activePositions.map(position => {
        const exchangePos = exchangePositions.find(p => p.symbol === position.symbol);
        
        if (exchangePos) {
          const currentPrice = exchangePos.markPrice || position.currentPrice;
          const unrealizedPnL = this.calculatePnL(position, currentPrice);
          
          return {
            ...position,
            currentPrice,
            unrealizedPnL,
            lastUpdateTimestamp: Date.now(),
          };
        }
        
        return position;
      });

      // Check for positions that need to be closed (stop loss/take profit hit)
      for (const position of updatedPositions) {
        if (this.shouldClosePosition(position)) {
          const closeEvent = await this.forceClosePosition(position);
          if (closeEvent) {
            events.push(closeEvent);
            updated = true;
          }
        }
      }

      return {
        state: {
          ...state,
          activePositions: updatedPositions.filter(p => p.status === 'open'),
        },
        events,
        updated,
      };

    } catch (error) {
      logger.error('Error updating positions:', error);
      return { state, events, updated: false };
    }
  }

  // Calculate P&L for a position
  private calculatePnL(position: Position, exitPrice: number): number {
    const priceDiff = position.side === 'long' 
      ? exitPrice - position.entryPrice
      : position.entryPrice - exitPrice;
    
    return priceDiff * position.size * position.leverage;
  }

  // Calculate stop loss price
  private calculateStopLoss(entryPrice: number, side: string): number {
    const atrMultiplier = 2; // 2 ATR stop loss
    const atrEstimate = entryPrice * 0.02; // 2% estimate
    
    return side === 'buy' 
      ? entryPrice - (atrMultiplier * atrEstimate)
      : entryPrice + (atrMultiplier * atrEstimate);
  }

  // Set stop loss order
  private async setStopLoss(position: Position, stopLossPrice: number): Promise<void> {
    try {
      const side = position.side === 'long' ? 'sell' : 'buy';
      
      // Create stop loss order (implementation depends on exchange capabilities)
      logger.info(`Stop loss set for position ${position.id} at ${formatCurrency(stopLossPrice)}`);
    } catch (error) {
      logger.error('Failed to set stop loss:', error);
    }
  }

  // Set take profit order
  private async setTakeProfit(position: Position, takeProfitPrice: number): Promise<void> {
    try {
      const side = position.side === 'long' ? 'sell' : 'buy';
      
      // Create take profit order (implementation depends on exchange capabilities)
      logger.info(`Take profit set for position ${position.id} at ${formatCurrency(takeProfitPrice)}`);
    } catch (error) {
      logger.error('Failed to set take profit:', error);
    }
  }

  // Check if position should be closed
  private shouldClosePosition(position: Position): boolean {
    // Check stop loss
    if (position.stopLoss) {
      if (position.side === 'long' && position.currentPrice <= position.stopLoss) {
        return true;
      }
      if (position.side === 'short' && position.currentPrice >= position.stopLoss) {
        return true;
      }
    }

    // Check take profit
    if (position.takeProfit) {
      if (position.side === 'long' && position.currentPrice >= position.takeProfit) {
        return true;
      }
      if (position.side === 'short' && position.currentPrice <= position.takeProfit) {
        return true;
      }
    }

    return false;
  }

  // Force close position
  private async forceClosePosition(position: Position): Promise<TradeEvent | null> {
    try {
      const side = position.side === 'long' ? 'sell' : 'buy';
      const order = await this.phemexService.createMarketOrder(
        position.symbol,
        side,
        position.size
      );

      const pnl = this.calculatePnL(position, order.price || position.currentPrice);
      const eventType = position.currentPrice <= (position.stopLoss || 0) ? 'stop_loss_hit' : 'take_profit_hit';

      return {
        id: uuidv4(),
        type: eventType,
        positionId: position.id,
        symbol: position.symbol,
        side,
        amount: position.size,
        price: order.price || position.currentPrice,
        pnl,
        timestamp: Date.now(),
        message: `${eventType.replace('_', ' ')} - Position closed at ${formatCurrency(order.price || position.currentPrice)}`,
      };
    } catch (error) {
      logger.error('Failed to force close position:', error);
      return null;
    }
  }

  // Update trade events in state
  private updateTradeEventsInState(state: TradingState, events: TradeEvent[]): TradingState {
    return {
      ...state,
      tradeEvents: [...state.tradeEvents, ...events],
    };
  }

  // Get execution statistics
  getExecutionStats() {
    const recentEvents = this.executionHistory.filter(e => 
      e.timestamp >= Date.now() - (24 * 60 * 60 * 1000) // Last 24 hours
    );

    const totalTrades = recentEvents.filter(e => e.type === 'position_opened').length;
    const totalPnL = recentEvents.reduce((sum, e) => sum + (e.pnl || 0), 0);
    const winningTrades = recentEvents.filter(e => (e.pnl || 0) > 0).length;
    const winRate = totalTrades > 0 ? winningTrades / totalTrades : 0;

    return {
      totalTrades,
      totalPnL,
      winRate,
      lastExecution: this.lastExecutionTimestamp,
      pendingOrders: this.pendingOrders.size,
    };
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const phemexHealth = await this.phemexService.healthCheck();
      return this.isActive && phemexHealth;
    } catch (error) {
      logger.error('OrderExecutionAgent health check failed:', error);
      return false;
    }
  }

  // Get agent status
  getStatus() {
    return {
      isActive: this.isActive,
      lastExecution: this.lastExecutionTimestamp,
      executionStats: this.getExecutionStats(),
    };
  }

  // Stop the agent
  async stop(): Promise<void> {
    try {
      this.isActive = false;
      this.pendingOrders.clear();
      this.removeAllListeners();
      
      logger.info('OrderExecutionAgent stopped');
    } catch (error) {
      logger.error('Error stopping OrderExecutionAgent:', error);
    }
  }
}
