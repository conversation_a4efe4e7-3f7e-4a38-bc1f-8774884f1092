#!/usr/bin/env node

/**
 * Startup script for Multi-AI Agent Trading System
 * Provides interactive setup and validation before starting the system
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function checkEnvironment() {
  console.log('🔍 Checking environment configuration...\n');

  const envPath = path.join(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env file not found!');
    console.log('📋 Please copy .env.example to .env and configure your API keys.\n');
    
    const createEnv = await question('Would you like to create .env from template? (y/n): ');
    
    if (createEnv.toLowerCase() === 'y') {
      const examplePath = path.join(process.cwd(), '.env.example');
      if (fs.existsSync(examplePath)) {
        fs.copyFileSync(examplePath, envPath);
        console.log('✅ .env file created from template');
        console.log('⚠️  Please edit .env with your actual API keys before continuing.\n');
      } else {
        console.log('❌ .env.example not found!');
      }
    }
    
    process.exit(1);
  }

  // Load environment variables
  require('dotenv').config();

  // Check required API keys
  const requiredKeys = [
    'XAI_API_KEY',
    'PHEMEX_API_KEY',
    'PHEMEX_SECRET',
    'TAAPI_API_KEY',
    'DISCORD_BOT_TOKEN',
    'DISCORD_CHANNEL_ID'
  ];

  const missingKeys = requiredKeys.filter(key => !process.env[key]);
  
  if (missingKeys.length > 0) {
    console.log('❌ Missing required environment variables:');
    missingKeys.forEach(key => console.log(`   - ${key}`));
    console.log('\n📝 Please add these to your .env file.\n');
    process.exit(1);
  }

  console.log('✅ All required environment variables found');
}

async function checkDependencies() {
  console.log('📦 Checking dependencies...\n');

  const packagePath = path.join(process.cwd(), 'package.json');
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');

  if (!fs.existsSync(packagePath)) {
    console.log('❌ package.json not found!');
    process.exit(1);
  }

  if (!fs.existsSync(nodeModulesPath)) {
    console.log('❌ node_modules not found!');
    console.log('📦 Please run: npm install\n');
    process.exit(1);
  }

  console.log('✅ Dependencies check passed');
}

async function checkBuild() {
  console.log('🔨 Checking build status...\n');

  const distPath = path.join(process.cwd(), 'dist');
  const mainJsPath = path.join(distPath, 'main.js');

  if (!fs.existsSync(mainJsPath)) {
    console.log('❌ Build files not found!');
    console.log('🔨 Please run: npm run build\n');
    
    const buildNow = await question('Would you like to build now? (y/n): ');
    
    if (buildNow.toLowerCase() === 'y') {
      console.log('🔨 Building project...');
      const { spawn } = require('child_process');
      
      return new Promise((resolve, reject) => {
        const build = spawn('npm', ['run', 'build'], { stdio: 'inherit' });
        
        build.on('close', (code) => {
          if (code === 0) {
            console.log('✅ Build completed successfully');
            resolve();
          } else {
            console.log('❌ Build failed');
            reject(new Error('Build failed'));
          }
        });
      });
    } else {
      process.exit(1);
    }
  }

  console.log('✅ Build files found');
}

async function showConfiguration() {
  console.log('\n📊 Current Configuration:');
  console.log('========================');
  console.log(`Trading Symbol: ${process.env.TRADING_SYMBOL || 'BTC/USD:USD'}`);
  console.log(`Leverage: ${process.env.LEVERAGE || '10'}x`);
  console.log(`Risk per Trade: ${process.env.RISK_PERCENTAGE || '1.5'}%`);
  console.log(`Timeframe: ${process.env.TIMEFRAME || '15m'}`);
  console.log(`Live Trading: ${process.env.ENABLE_LIVE_TRADING === 'true' ? '🔴 ENABLED' : '🟡 DISABLED (Sandbox)'}`);
  console.log(`Log Level: ${process.env.LOG_LEVEL || 'info'}`);
  console.log('========================\n');
}

async function confirmStart() {
  if (process.env.ENABLE_LIVE_TRADING === 'true') {
    console.log('⚠️  WARNING: LIVE TRADING IS ENABLED!');
    console.log('💰 This system will execute real trades with real money.');
    console.log('📉 You could lose money. Only proceed if you understand the risks.\n');
    
    const confirm1 = await question('Do you understand the risks? (yes/no): ');
    if (confirm1.toLowerCase() !== 'yes') {
      console.log('❌ Startup cancelled for safety.');
      process.exit(0);
    }
    
    const confirm2 = await question('Are you sure you want to enable live trading? (yes/no): ');
    if (confirm2.toLowerCase() !== 'yes') {
      console.log('❌ Startup cancelled for safety.');
      process.exit(0);
    }
    
    console.log('🚨 LIVE TRADING CONFIRMED - Starting in 5 seconds...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  } else {
    console.log('🟡 Sandbox mode - No real trades will be executed.');
    const proceed = await question('Start the trading system? (y/n): ');
    
    if (proceed.toLowerCase() !== 'y') {
      console.log('❌ Startup cancelled.');
      process.exit(0);
    }
  }
}

async function startSystem() {
  console.log('\n🚀 Starting Multi-AI Agent Trading System...\n');
  
  // Start the main system
  require('../dist/main.js');
}

async function main() {
  try {
    console.log('🤖 Multi-AI Agent Trading System Startup\n');
    console.log('==========================================\n');

    await checkEnvironment();
    await checkDependencies();
    await checkBuild();
    await showConfiguration();
    await confirmStart();
    
    rl.close();
    
    await startSystem();
    
  } catch (error) {
    console.error('❌ Startup failed:', error.message);
    rl.close();
    process.exit(1);
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n👋 Startup cancelled by user.');
  rl.close();
  process.exit(0);
});

if (require.main === module) {
  main();
}
