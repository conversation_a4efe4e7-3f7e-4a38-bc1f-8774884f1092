// Test setup file for Jest
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };

beforeAll(() => {
  // Mock console methods for cleaner test output
  console.log = jest.fn();
  console.info = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  // Restore console methods
  Object.assign(console, originalConsole);
});

// Global test timeout
jest.setTimeout(30000);

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.ENABLE_LIVE_TRADING = 'false';
process.env.LOG_LEVEL = 'error';

// Mock API keys for testing (these won't be used in actual API calls)
process.env.XAI_API_KEY = 'test-xai-key';
process.env.PHEMEX_API_KEY = 'test-phemex-key';
process.env.PHEMEX_SECRET = 'test-phemex-secret';
process.env.TAAPI_API_KEY = 'test-taapi-key';
process.env.DISCORD_BOT_TOKEN = 'test-discord-token';
process.env.DISCORD_CHANNEL_ID = '123456789012345678';

// Export test utilities
export const mockTrade = (side: 'buy' | 'sell', amount: number, price: number) => ({
  id: Math.random().toString(36),
  side,
  amount,
  price,
  timestamp: Date.now(),
  symbol: 'BTC/USD:USD',
  type: 'market',
  cost: amount * price,
  fee: { cost: 0, currency: 'USD' },
});

export const mockOrderBook = (bidPrice: number, askPrice: number, depth: number = 5) => ({
  bids: Array.from({ length: depth }, (_, i) => [bidPrice - i, Math.random() * 10 + 1]),
  asks: Array.from({ length: depth }, (_, i) => [askPrice + i, Math.random() * 10 + 1]),
  timestamp: Date.now(),
  datetime: new Date().toISOString(),
  nonce: undefined,
});

export const mockOHLCV = (open: number, high: number, low: number, close: number, volume: number) => [
  Date.now(),
  open,
  high,
  low,
  close,
  volume,
];

export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
