import { EventEmitter } from 'events';
import { TradingState, AgentInput, AgentOutput, TradeDecision, TradingConfig, TradingSystemError } from '@/types';
import { GrokService } from '@/services/GrokService';
import { PhemexService } from '@/services/PhemexService';
import { logger, calculateCVDMomentum } from '@/utils';
import { CONSTANTS } from '@/config';

export class DecisionMakingAgent extends EventEmitter {
  private grokService: GrokService;
  private phemexService: PhemexService;
  private config: TradingConfig;
  private isActive: boolean = false;
  private lastDecisionTimestamp: number = 0;
  private decisionHistory: TradeDecision[] = [];

  constructor(grokService: GrokService, phemexService: PhemexService, config: TradingConfig) {
    super();
    this.grokService = grokService;
    this.phemexService = phemexService;
    this.config = config;

    logger.info('DecisionMakingAgent initialized', {
      enableLiveTrading: this.config.enableLiveTrading,
      riskPercentage: this.config.riskPercentage,
    });
  }

  // Initialize the agent
  async initialize(): Promise<void> {
    try {
      this.isActive = true;
      this.emit('initialized');
      
      logger.info('DecisionMakingAgent started successfully');
    } catch (error) {
      logger.error('Failed to initialize DecisionMakingAgent:', error);
      throw new TradingSystemError(
        'Failed to initialize DecisionMakingAgent',
        'DECISION_MAKING_INIT_ERROR',
        'DecisionMakingAgent',
        error as Error
      );
    }
  }

  // Process agent input and make trading decisions
  async process(state: TradingState, input: AgentInput): Promise<{ state: TradingState; output: AgentOutput }> {
    try {
      const startTime = Date.now();
      let updated = false;
      let decision: TradeDecision | null = null;

      // Make decision if we have new signals or this is a timer trigger
      if (this.shouldMakeDecision(state, input)) {
        decision = await this.makeTradeDecision(state);
        
        if (decision) {
          state = this.updateDecisionInState(state, decision);
          updated = true;
        }
      }

      const processingTime = Date.now() - startTime;

      const output: AgentOutput = {
        success: true,
        data: {
          updated,
          processingTime,
          decision,
          decisionAction: decision?.action || 'none',
          confidence: decision?.confidence || 0,
        },
        timestamp: Date.now(),
      };

      if (updated && decision) {
        logger.info('DecisionMakingAgent made decision', {
          action: decision.action,
          confidence: decision.confidence,
          reasoning: decision.reasoning.substring(0, 100) + '...',
        });

        this.emit('decision_made', decision);
      }

      return { state, output };
    } catch (error) {
      logger.error('DecisionMakingAgent processing error:', error);
      
      const output: AgentOutput = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      };

      return { state, output };
    }
  }

  // Determine if we should make a decision
  private shouldMakeDecision(state: TradingState, input: AgentInput): boolean {
    // Make decision if we have new signals with sufficient confidence
    if (state.signals.length > 0) {
      const strongSignals = state.signals.filter(s => s.confidence >= CONSTANTS.MIN_TRADE_CONFIDENCE);
      if (strongSignals.length > 0) {
        return true;
      }
    }

    // Make decision on timer if we have open positions (for risk management)
    if (input.trigger === 'timer' && state.activePositions.length > 0) {
      return true;
    }

    return false;
  }

  // Make trading decision using Grok 3
  private async makeTradeDecision(state: TradingState): Promise<TradeDecision | null> {
    try {
      // Get account information
      const accountInfo = await this.getAccountInfo();
      
      // Prepare market data
      const marketData = this.prepareMarketData(state);
      
      // Get the strongest signal
      const primarySignal = this.getPrimarySignal(state);
      
      if (!primarySignal) {
        logger.debug('No primary signal available for decision making');
        return null;
      }

      // Check if we should assess risk for existing positions
      if (state.activePositions.length > 0) {
        const riskAssessment = await this.assessPositionRisk(state, marketData, accountInfo.balance);
        
        if (riskAssessment.shouldClose) {
          return this.createClosePositionDecision(state, riskAssessment);
        }
      }

      // Make trade decision using Grok 3
      const decision = await this.grokService.makeTradeDecision(
        primarySignal,
        marketData,
        accountInfo
      );

      // Validate and enhance decision
      const validatedDecision = this.validateAndEnhanceDecision(decision, state, accountInfo);
      
      // Add to decision history
      this.decisionHistory.push(validatedDecision);
      this.lastDecisionTimestamp = Date.now();

      return validatedDecision;
    } catch (error) {
      logger.error('Error making trade decision:', error);
      return null;
    }
  }

  // Get account information
  private async getAccountInfo(): Promise<{
    balance: number;
    openPositions: number;
    dailyPnL: number;
    riskPercentage: number;
  }> {
    try {
      const balance = await this.phemexService.getBalance();
      const positions = await this.phemexService.getPositions();
      
      // Calculate daily P&L (simplified)
      const dailyPnL = positions.reduce((sum, pos) => sum + (pos.unrealizedPnl || 0), 0);
      
      return {
        balance: balance.total || 10000, // Default for testing
        openPositions: positions.length,
        dailyPnL,
        riskPercentage: this.config.riskPercentage,
      };
    } catch (error) {
      logger.warn('Failed to get account info, using defaults:', error);
      
      return {
        balance: 10000, // Default balance for testing
        openPositions: 0,
        dailyPnL: 0,
        riskPercentage: this.config.riskPercentage,
      };
    }
  }

  // Prepare market data for Grok analysis
  private prepareMarketData(state: TradingState) {
    const currentPrice = state.orderBook?.bids[0]?.[0] || 0;
    const latestCVD = state.cvdHistory[state.cvdHistory.length - 1]?.cvd || 0;
    const momentum = calculateCVDMomentum(state.cvdHistory);
    const volume = state.recentTrades.reduce((sum, trade) => sum + trade.amount, 0);
    
    // Calculate volatility from recent OHLCV
    let volatility = 0;
    if (state.ohlcv.length >= 2) {
      const recentCandles = state.ohlcv.slice(-10);
      const returns = recentCandles.slice(1).map((candle, i) => 
        Math.log(candle[4] / recentCandles[i][4])
      );
      const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
      volatility = Math.sqrt(
        returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length
      );
    }

    // Determine trend
    let trend = 'sideways';
    if (state.ohlcv.length >= 3) {
      const closes = state.ohlcv.slice(-3).map(candle => candle[4]);
      if (closes[2] > closes[1] && closes[1] > closes[0]) {
        trend = 'uptrend';
      } else if (closes[2] < closes[1] && closes[1] < closes[0]) {
        trend = 'downtrend';
      }
    }

    return {
      price: currentPrice,
      orderBookImbalance: state.imbalance,
      cvd: latestCVD,
      momentum,
      volume,
      volatility,
      trend,
    };
  }

  // Get the primary (strongest) signal
  private getPrimarySignal(state: TradingState) {
    if (state.signals.length === 0) return null;
    
    // Return the signal with highest combined score (strength * confidence)
    return state.signals.reduce((best, current) => {
      const bestScore = best.strength * best.confidence;
      const currentScore = current.strength * current.confidence;
      return currentScore > bestScore ? current : best;
    });
  }

  // Assess risk for existing positions
  private async assessPositionRisk(state: TradingState, marketData: any, accountBalance: number) {
    try {
      return await this.grokService.assessRisk(
        state.activePositions,
        marketData,
        accountBalance
      );
    } catch (error) {
      logger.error('Risk assessment failed:', error);
      return {
        shouldClose: false,
        reasoning: 'Risk assessment failed',
        urgency: 'low' as const,
      };
    }
  }

  // Create close position decision
  private createClosePositionDecision(state: TradingState, riskAssessment: any): TradeDecision {
    const position = state.activePositions[0]; // Close first position
    
    return {
      action: 'close_position',
      symbol: position.symbol,
      side: position.side === 'long' ? 'sell' : 'buy',
      amount: position.size,
      leverage: position.leverage,
      reasoning: `Risk management: ${riskAssessment.reasoning}`,
      confidence: riskAssessment.urgency === 'high' ? 0.9 : 0.7,
      riskReward: 0,
      timestamp: Date.now(),
    };
  }

  // Validate and enhance decision
  private validateAndEnhanceDecision(
    decision: TradeDecision,
    state: TradingState,
    accountInfo: any
  ): TradeDecision {
    // Ensure decision has required fields
    const enhancedDecision = {
      ...decision,
      symbol: this.config.symbol,
      leverage: this.config.leverage,
      timestamp: Date.now(),
    };

    // Validate confidence level
    if (enhancedDecision.confidence < CONSTANTS.MIN_TRADE_CONFIDENCE) {
      enhancedDecision.action = 'hold';
      enhancedDecision.reasoning += ' (Confidence too low for trade execution)';
    }

    // Validate position size
    if (enhancedDecision.amount > 0) {
      const maxPositionSize = accountInfo.balance * CONSTANTS.MAX_POSITION_SIZE_MULTIPLIER;
      if (enhancedDecision.amount * (enhancedDecision.price || 0) > maxPositionSize) {
        enhancedDecision.amount = maxPositionSize / (enhancedDecision.price || 1);
        enhancedDecision.reasoning += ' (Position size adjusted for risk management)';
      }
    }

    // Disable live trading if not enabled
    if (!this.config.enableLiveTrading && enhancedDecision.action !== 'hold') {
      logger.warn('Live trading disabled, converting decision to hold');
      enhancedDecision.action = 'hold';
      enhancedDecision.reasoning += ' (Live trading disabled)';
    }

    return enhancedDecision;
  }

  // Update decision in state
  private updateDecisionInState(state: TradingState, decision: TradeDecision): TradingState {
    return {
      ...state,
      decision,
    };
  }

  // Get decision statistics
  getDecisionStats() {
    const recentDecisions = this.decisionHistory.filter(d => 
      d.timestamp >= Date.now() - (24 * 60 * 60 * 1000) // Last 24 hours
    );
    
    const actionCounts = recentDecisions.reduce((counts, decision) => {
      counts[decision.action] = (counts[decision.action] || 0) + 1;
      return counts;
    }, {} as { [key: string]: number });

    const avgConfidence = recentDecisions.length > 0
      ? recentDecisions.reduce((sum, d) => sum + d.confidence, 0) / recentDecisions.length
      : 0;

    return {
      totalDecisions: recentDecisions.length,
      actionCounts,
      avgConfidence,
      lastDecision: this.lastDecisionTimestamp,
      grokUsage: this.grokService.getUsageStats(),
    };
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const grokHealth = await this.grokService.healthCheck();
      return this.isActive && grokHealth;
    } catch (error) {
      logger.error('DecisionMakingAgent health check failed:', error);
      return false;
    }
  }

  // Get agent status
  getStatus() {
    return {
      isActive: this.isActive,
      lastDecision: this.lastDecisionTimestamp,
      decisionStats: this.getDecisionStats(),
    };
  }

  // Stop the agent
  async stop(): Promise<void> {
    try {
      this.isActive = false;
      this.removeAllListeners();
      
      logger.info('DecisionMakingAgent stopped');
    } catch (error) {
      logger.error('Error stopping DecisionMakingAgent:', error);
    }
  }
}
