import axios, { AxiosInstance } from 'axios';
import { APIConfig, TradeDecision, TradingSignal, GrokPrompt, GrokResponse, TradingSystemError } from '@/types';
import { logger, retryWithBackoff } from '@/utils';
import { CONSTANTS } from '@/config';

export class GrokService {
  private client: AxiosInstance;
  private config: APIConfig['grok'];
  private requestCount: number = 0;
  private lastRequestTime: number = 0;

  constructor(apiConfig: APIConfig['grok']) {
    this.config = apiConfig;
    
    this.client = axios.create({
      baseURL: this.config.baseURL,
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 second timeout
    });

    logger.info('GrokService initialized', {
      baseURL: this.config.baseURL,
      model: this.config.model,
    });
  }

  // Main decision-making method
  async makeTradeDecision(
    signal: TradingSignal,
    marketData: {
      price: number;
      orderBookImbalance: number;
      cvd: number;
      momentum: number;
      volume: number;
      volatility?: number;
      trend?: string;
    },
    accountInfo: {
      balance: number;
      openPositions: number;
      dailyPnL: number;
      riskPercentage: number;
    }
  ): Promise<TradeDecision> {
    try {
      const prompt = this.createTradingPrompt(signal, marketData, accountInfo);
      const response = await this.callGrokAPI(prompt);
      
      return this.parseTradeDecision(response, signal, marketData);
    } catch (error) {
      logger.error('Failed to make trade decision:', error);
      throw new TradingSystemError(
        'Failed to make trade decision',
        'GROK_DECISION_ERROR',
        'GrokService',
        error as Error
      );
    }
  }

  // Create comprehensive trading prompt for Grok
  private createTradingPrompt(
    signal: TradingSignal,
    marketData: any,
    accountInfo: any
  ): GrokPrompt[] {
    const systemPrompt = `You are an expert cryptocurrency day trader specializing in perpetual swap contracts. Your role is to analyze market data and make precise trading decisions based on technical indicators, order book analysis, and risk management principles.

TRADING RULES:
1. Only trade when confidence is high (>70%)
2. Always consider risk-reward ratio (minimum 1:2)
3. Use proper position sizing based on account balance and risk percentage
4. Set appropriate stop-loss and take-profit levels
5. Consider market volatility and current trend
6. Factor in order book imbalances and volume delta

RESPONSE FORMAT:
Respond with a JSON object containing:
{
  "action": "enter_long" | "enter_short" | "hold" | "close_position",
  "confidence": 0.0-1.0,
  "reasoning": "detailed explanation",
  "entry_price": number or null,
  "stop_loss": number or null,
  "take_profit": number or null,
  "position_size": number or null,
  "risk_reward": number or null
}`;

    const userPrompt = `MARKET ANALYSIS REQUEST:

SIGNAL DATA:
- Signal Type: ${signal.type}
- Signal Strength: ${signal.strength}/100
- Signal Confidence: ${(signal.confidence * 100).toFixed(1)}%
- Reasons: ${signal.reasons.join(', ')}

MARKET DATA:
- Current Price: $${marketData.price.toFixed(2)}
- CVD (Cumulative Volume Delta): ${marketData.cvd.toFixed(2)}
- Order Book Imbalance: ${(marketData.orderBookImbalance * 100).toFixed(2)}%
- Momentum: ${marketData.momentum.toFixed(2)}
- Volume: ${marketData.volume.toFixed(2)}
${marketData.volatility ? `- Volatility: ${(marketData.volatility * 100).toFixed(2)}%` : ''}
${marketData.trend ? `- Trend: ${marketData.trend}` : ''}

ACCOUNT INFO:
- Account Balance: $${accountInfo.balance.toFixed(2)}
- Open Positions: ${accountInfo.openPositions}
- Daily P&L: $${accountInfo.dailyPnL.toFixed(2)}
- Risk Per Trade: ${accountInfo.riskPercentage}%

ANALYSIS REQUEST:
Based on the above data, should I enter a trade? If yes, provide specific entry, stop-loss, and take-profit levels. Consider:
1. Signal strength and confluence of indicators
2. Risk-reward ratio and position sizing
3. Current market conditions and volatility
4. Account risk management

Provide your analysis in the specified JSON format.`;

    return [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];
  }

  // Call Grok API with rate limiting
  private async callGrokAPI(messages: GrokPrompt[]): Promise<string> {
    // Rate limiting
    await this.enforceRateLimit();

    try {
      const response = await retryWithBackoff(async () => {
        return await this.client.post('/chat/completions', {
          model: this.config.model,
          messages: messages,
          temperature: 0.1,
          max_tokens: 1000,
          top_p: 0.9,
        });
      });

      this.requestCount++;
      this.lastRequestTime = Date.now();

      const content = response.data.choices[0]?.message?.content;
      if (!content) {
        throw new Error('Empty response from Grok API');
      }

      logger.debug('Grok API response received', {
        model: this.config.model,
        tokens: response.data.usage?.total_tokens,
      });

      return content;
    } catch (error) {
      logger.error('Grok API call failed:', error);
      throw error;
    }
  }

  // Parse Grok response into TradeDecision
  private parseTradeDecision(
    response: string,
    signal: TradingSignal,
    marketData: any
  ): TradeDecision {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in Grok response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      // Validate required fields
      if (!parsed.action || typeof parsed.confidence !== 'number') {
        throw new Error('Invalid response format from Grok');
      }

      // Map action to our format
      const actionMap: { [key: string]: TradeDecision['action'] } = {
        'enter_long': 'enter_long',
        'enter_short': 'enter_short',
        'hold': 'hold',
        'close_position': 'close_position',
      };

      const action = actionMap[parsed.action];
      if (!action) {
        throw new Error(`Invalid action: ${parsed.action}`);
      }

      // Determine side based on action
      let side: 'buy' | 'sell' = 'buy';
      if (action === 'enter_short') {
        side = 'sell';
      }

      const decision: TradeDecision = {
        action,
        symbol: 'BTC/USD:USD', // Default symbol
        side,
        amount: parsed.position_size || 0,
        price: parsed.entry_price,
        stopLoss: parsed.stop_loss,
        takeProfit: parsed.take_profit,
        leverage: 10, // Default leverage
        reasoning: parsed.reasoning || 'No reasoning provided',
        confidence: Math.max(0, Math.min(1, parsed.confidence)),
        riskReward: parsed.risk_reward || 0,
        timestamp: Date.now(),
      };

      logger.info('Trade decision parsed from Grok', {
        action: decision.action,
        confidence: decision.confidence,
        riskReward: decision.riskReward,
      });

      return decision;
    } catch (error) {
      logger.error('Failed to parse Grok response:', error);
      
      // Return a safe default decision
      return {
        action: 'hold',
        symbol: 'BTC/USD:USD',
        side: 'buy',
        amount: 0,
        leverage: 10,
        reasoning: `Failed to parse Grok response: ${error}`,
        confidence: 0,
        riskReward: 0,
        timestamp: Date.now(),
      };
    }
  }

  // Risk assessment for existing positions
  async assessRisk(
    positions: any[],
    marketData: any,
    accountBalance: number
  ): Promise<{
    shouldClose: boolean;
    reasoning: string;
    urgency: 'low' | 'medium' | 'high';
  }> {
    try {
      const prompt: GrokPrompt[] = [
        {
          role: 'system',
          content: `You are a risk management expert for cryptocurrency trading. Analyze current positions and market conditions to determine if any positions should be closed immediately.

RESPONSE FORMAT:
{
  "should_close": boolean,
  "reasoning": "detailed explanation",
  "urgency": "low" | "medium" | "high"
}`
        },
        {
          role: 'user',
          content: `RISK ASSESSMENT REQUEST:

CURRENT POSITIONS:
${positions.map(pos => `- ${pos.symbol}: ${pos.side} ${pos.size} at $${pos.entryPrice} (P&L: $${pos.unrealizedPnL})`).join('\n')}

MARKET DATA:
- Current Price: $${marketData.price}
- CVD: ${marketData.cvd}
- Volatility: ${marketData.volatility || 'N/A'}
- Order Book Imbalance: ${(marketData.orderBookImbalance * 100).toFixed(2)}%

ACCOUNT:
- Balance: $${accountBalance}
- Total Unrealized P&L: $${positions.reduce((sum, pos) => sum + pos.unrealizedPnL, 0)}

Should any positions be closed immediately due to risk concerns?`
        }
      ];

      const response = await this.callGrokAPI(prompt);
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          shouldClose: parsed.should_close || false,
          reasoning: parsed.reasoning || 'No reasoning provided',
          urgency: parsed.urgency || 'low',
        };
      }

      return {
        shouldClose: false,
        reasoning: 'Failed to parse risk assessment',
        urgency: 'low',
      };
    } catch (error) {
      logger.error('Risk assessment failed:', error);
      return {
        shouldClose: false,
        reasoning: `Risk assessment error: ${error}`,
        urgency: 'low',
      };
    }
  }

  // Rate limiting enforcement
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < CONSTANTS.API_RATE_LIMIT_DELAY) {
      const delay = CONSTANTS.API_RATE_LIMIT_DELAY - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const testPrompt: GrokPrompt[] = [
        { role: 'user', content: 'Respond with "OK" if you are working correctly.' }
      ];
      
      const response = await this.callGrokAPI(testPrompt);
      return response.toLowerCase().includes('ok');
    } catch (error) {
      logger.error('Grok health check failed:', error);
      return false;
    }
  }

  // Get usage statistics
  getUsageStats() {
    return {
      requestCount: this.requestCount,
      lastRequestTime: this.lastRequestTime,
      model: this.config.model,
    };
  }
}
