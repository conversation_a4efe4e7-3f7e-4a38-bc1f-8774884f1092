import ccxt, { Exchange, OrderBook, Trade, OHLCV, Order } from 'ccxt';
import { EventEmitter } from 'events';
import { APIConfig, TradingConfig, Position, TradingSystemError } from '@/types';
import { logger, retryWithBackoff, sleep } from '@/utils';
import { CONSTANTS } from '@/config';

export class PhemexService extends EventEmitter {
  private exchange: Exchange;
  private config: APIConfig['phemex'];
  private tradingConfig: TradingConfig;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private subscriptions: Set<string> = new Set();
  private watchIntervals: Map<string, NodeJS.Timeout> = new Map();

  constructor(apiConfig: APIConfig['phemex'], tradingConfig: TradingConfig) {
    super();
    this.config = apiConfig;
    this.tradingConfig = tradingConfig;
    
    // Initialize CCXT Phemex exchange
    this.exchange = new ccxt.phemex({
      apiKey: this.config.apiKey,
      secret: this.config.secret,
      password: this.config.passphrase,
      sandbox: this.config.sandbox,
      enableRateLimit: true,
      rateLimit: 100, // 100ms between requests
      options: {
        defaultType: 'swap', // For perpetual swaps
        recvWindow: 10000,
      },
    });

    logger.info('PhemexService initialized', {
      sandbox: this.config.sandbox,
      symbol: this.tradingConfig.symbol,
      leverage: this.tradingConfig.leverage,
    });
  }

  // Initialize connection and set leverage
  async initialize(): Promise<void> {
    try {
      // Load markets
      await this.exchange.loadMarkets();
      logger.info('Markets loaded successfully');

      // Set leverage for the trading symbol
      if (this.tradingConfig.enableLiveTrading) {
        await this.setLeverage(this.tradingConfig.symbol, this.tradingConfig.leverage);
        logger.info(`Leverage set to ${this.tradingConfig.leverage}x for ${this.tradingConfig.symbol}`);
      }

      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('connected');
      
    } catch (error) {
      logger.error('Failed to initialize Phemex service:', error);
      throw new TradingSystemError(
        'Failed to initialize Phemex service',
        'PHEMEX_INIT_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Set leverage for a symbol
  async setLeverage(symbol: string, leverage: number): Promise<void> {
    try {
      await retryWithBackoff(async () => {
        await this.exchange.setLeverage(leverage, symbol);
      });
      
      logger.info(`Leverage set to ${leverage}x for ${symbol}`);
    } catch (error) {
      logger.error(`Failed to set leverage for ${symbol}:`, error);
      throw new TradingSystemError(
        `Failed to set leverage for ${symbol}`,
        'LEVERAGE_SET_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Subscribe to order book updates
  async subscribeToOrderBook(symbol: string): Promise<void> {
    const subscriptionKey = `orderbook_${symbol}`;
    
    if (this.subscriptions.has(subscriptionKey)) {
      logger.warn(`Already subscribed to order book for ${symbol}`);
      return;
    }

    try {
      this.subscriptions.add(subscriptionKey);
      
      // Start watching order book
      this.watchOrderBook(symbol);
      
      logger.info(`Subscribed to order book for ${symbol}`);
    } catch (error) {
      this.subscriptions.delete(subscriptionKey);
      logger.error(`Failed to subscribe to order book for ${symbol}:`, error);
      throw new TradingSystemError(
        `Failed to subscribe to order book for ${symbol}`,
        'ORDERBOOK_SUBSCRIPTION_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Subscribe to trade updates
  async subscribeToTrades(symbol: string): Promise<void> {
    const subscriptionKey = `trades_${symbol}`;
    
    if (this.subscriptions.has(subscriptionKey)) {
      logger.warn(`Already subscribed to trades for ${symbol}`);
      return;
    }

    try {
      this.subscriptions.add(subscriptionKey);
      
      // Start watching trades
      this.watchTrades(symbol);
      
      logger.info(`Subscribed to trades for ${symbol}`);
    } catch (error) {
      this.subscriptions.delete(subscriptionKey);
      logger.error(`Failed to subscribe to trades for ${symbol}:`, error);
      throw new TradingSystemError(
        `Failed to subscribe to trades for ${symbol}`,
        'TRADES_SUBSCRIPTION_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Subscribe to OHLCV updates
  async subscribeToOHLCV(symbol: string, timeframe: string): Promise<void> {
    const subscriptionKey = `ohlcv_${symbol}_${timeframe}`;
    
    if (this.subscriptions.has(subscriptionKey)) {
      logger.warn(`Already subscribed to OHLCV for ${symbol} ${timeframe}`);
      return;
    }

    try {
      this.subscriptions.add(subscriptionKey);
      
      // Start watching OHLCV
      this.watchOHLCV(symbol, timeframe);
      
      logger.info(`Subscribed to OHLCV for ${symbol} ${timeframe}`);
    } catch (error) {
      this.subscriptions.delete(subscriptionKey);
      logger.error(`Failed to subscribe to OHLCV for ${symbol} ${timeframe}:`, error);
      throw new TradingSystemError(
        `Failed to subscribe to OHLCV for ${symbol} ${timeframe}`,
        'OHLCV_SUBSCRIPTION_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Watch order book with error handling and reconnection
  private async watchOrderBook(symbol: string): Promise<void> {
    const watch = async () => {
      try {
        while (this.subscriptions.has(`orderbook_${symbol}`)) {
          const orderBook = await this.exchange.watchOrderBook(symbol);
          this.emit('orderbook', { symbol, data: orderBook });
          
          // Reset reconnect attempts on successful data
          this.reconnectAttempts = 0;
        }
      } catch (error) {
        logger.error(`Order book watch error for ${symbol}:`, error);
        await this.handleReconnection(`orderbook_${symbol}`, () => this.watchOrderBook(symbol));
      }
    };

    watch();
  }

  // Watch trades with error handling and reconnection
  private async watchTrades(symbol: string): Promise<void> {
    const watch = async () => {
      try {
        while (this.subscriptions.has(`trades_${symbol}`)) {
          const trades = await this.exchange.watchTrades(symbol);
          this.emit('trades', { symbol, data: trades });
          
          // Reset reconnect attempts on successful data
          this.reconnectAttempts = 0;
        }
      } catch (error) {
        logger.error(`Trades watch error for ${symbol}:`, error);
        await this.handleReconnection(`trades_${symbol}`, () => this.watchTrades(symbol));
      }
    };

    watch();
  }

  // Watch OHLCV with error handling and reconnection
  private async watchOHLCV(symbol: string, timeframe: string): Promise<void> {
    const watch = async () => {
      try {
        while (this.subscriptions.has(`ohlcv_${symbol}_${timeframe}`)) {
          const ohlcv = await this.exchange.watchOHLCV(symbol, timeframe);
          this.emit('ohlcv', { symbol, timeframe, data: ohlcv });
          
          // Reset reconnect attempts on successful data
          this.reconnectAttempts = 0;
        }
      } catch (error) {
        logger.error(`OHLCV watch error for ${symbol} ${timeframe}:`, error);
        await this.handleReconnection(`ohlcv_${symbol}_${timeframe}`, () => this.watchOHLCV(symbol, timeframe));
      }
    };

    watch();
  }

  // Handle reconnection logic
  private async handleReconnection(subscriptionKey: string, reconnectFn: () => Promise<void>): Promise<void> {
    if (this.reconnectAttempts >= CONSTANTS.MAX_RECONNECT_ATTEMPTS) {
      logger.error(`Max reconnection attempts reached for ${subscriptionKey}`);
      this.subscriptions.delete(subscriptionKey);
      this.emit('error', new TradingSystemError(
        `Max reconnection attempts reached for ${subscriptionKey}`,
        'MAX_RECONNECT_ATTEMPTS',
        'PhemexService'
      ));
      return;
    }

    this.reconnectAttempts++;
    const delay = CONSTANTS.RECONNECT_DELAY * Math.pow(2, this.reconnectAttempts - 1);
    
    logger.warn(`Reconnecting ${subscriptionKey} in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    await sleep(delay);
    
    if (this.subscriptions.has(subscriptionKey)) {
      await reconnectFn();
    }
  }

  // Create market order
  async createMarketOrder(symbol: string, side: 'buy' | 'sell', amount: number): Promise<Order> {
    try {
      const order = await retryWithBackoff(async () => {
        return await this.exchange.createMarketOrder(symbol, side, amount);
      });

      logger.info(`Market order created:`, {
        symbol,
        side,
        amount,
        orderId: order.id,
      });

      return order;
    } catch (error) {
      logger.error(`Failed to create market order:`, error);
      throw new TradingSystemError(
        `Failed to create market order for ${symbol}`,
        'ORDER_CREATE_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Create limit order
  async createLimitOrder(symbol: string, side: 'buy' | 'sell', amount: number, price: number): Promise<Order> {
    try {
      const order = await retryWithBackoff(async () => {
        return await this.exchange.createLimitOrder(symbol, side, amount, price);
      });

      logger.info(`Limit order created:`, {
        symbol,
        side,
        amount,
        price,
        orderId: order.id,
      });

      return order;
    } catch (error) {
      logger.error(`Failed to create limit order:`, error);
      throw new TradingSystemError(
        `Failed to create limit order for ${symbol}`,
        'ORDER_CREATE_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Cancel order
  async cancelOrder(orderId: string, symbol: string): Promise<void> {
    try {
      await retryWithBackoff(async () => {
        await this.exchange.cancelOrder(orderId, symbol);
      });

      logger.info(`Order cancelled: ${orderId}`);
    } catch (error) {
      logger.error(`Failed to cancel order ${orderId}:`, error);
      throw new TradingSystemError(
        `Failed to cancel order ${orderId}`,
        'ORDER_CANCEL_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Get account balance
  async getBalance(): Promise<any> {
    try {
      const balance = await retryWithBackoff(async () => {
        return await this.exchange.fetchBalance();
      });

      return balance;
    } catch (error) {
      logger.error('Failed to fetch balance:', error);
      throw new TradingSystemError(
        'Failed to fetch balance',
        'BALANCE_FETCH_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Get open positions
  async getPositions(symbol?: string): Promise<any[]> {
    try {
      const positions = await retryWithBackoff(async () => {
        return await this.exchange.fetchPositions(symbol ? [symbol] : undefined);
      });

      return positions.filter(pos => pos.size > 0);
    } catch (error) {
      logger.error('Failed to fetch positions:', error);
      throw new TradingSystemError(
        'Failed to fetch positions',
        'POSITIONS_FETCH_ERROR',
        'PhemexService',
        error as Error
      );
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.exchange.fetchStatus();
      return true;
    } catch (error) {
      logger.error('Phemex health check failed:', error);
      return false;
    }
  }

  // Cleanup and disconnect
  async disconnect(): Promise<void> {
    try {
      // Clear all subscriptions
      this.subscriptions.clear();
      
      // Clear intervals
      this.watchIntervals.forEach(interval => clearInterval(interval));
      this.watchIntervals.clear();
      
      // Close exchange connection
      if (this.exchange && typeof this.exchange.close === 'function') {
        await this.exchange.close();
      }
      
      this.isConnected = false;
      this.emit('disconnected');
      
      logger.info('PhemexService disconnected');
    } catch (error) {
      logger.error('Error during disconnect:', error);
    }
  }

  // Getters
  get connected(): boolean {
    return this.isConnected;
  }

  get activeSubscriptions(): string[] {
    return Array.from(this.subscriptions);
  }
}
