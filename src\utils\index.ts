import { Trade, OrderBook } from 'ccxt';
import { CVDHistoryEntry, VWAPData, TradingSignal, LogLevel, LogEntry } from '@/types';
import { CONSTANTS } from '@/config';
import winston from 'winston';

// Logger setup
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'trading-system' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ],
});

// CVD Calculation with Exponential Decay
export function calculateCVD(trades: Trade[], decayFactor: number = 0.001): number {
  if (trades.length === 0) return 0;
  
  const now = Date.now();
  let weightedCVD = 0;
  let totalWeight = 0;
  
  for (const trade of trades) {
    const timeDiff = (now - trade.timestamp) / (1000 * 60); // Minutes
    const weight = Math.exp(-decayFactor * timeDiff);
    const volume = trade.side === 'buy' ? trade.amount : -trade.amount;
    
    weightedCVD += volume * weight;
    totalWeight += weight;
  }
  
  return totalWeight > 0 ? weightedCVD / totalWeight : 0;
}

// Order Book Imbalance Detection using VWAP
export function detectOrderBookImbalance(orderBook: OrderBook, levels: number = 20): VWAPData {
  if (!orderBook || !orderBook.bids || !orderBook.asks) {
    throw new Error('Invalid order book data');
  }
  
  const bids = orderBook.bids.slice(0, levels);
  const asks = orderBook.asks.slice(0, levels);
  
  if (bids.length === 0 || asks.length === 0) {
    throw new Error('Insufficient order book depth');
  }
  
  // Calculate VWAP for bids and asks
  const bidVWAP = calculateVWAP(bids);
  const askVWAP = calculateVWAP(asks);
  
  // Calculate imbalance ratio
  const midPrice = (bidVWAP + askVWAP) / 2;
  const imbalanceRatio = (bidVWAP - askVWAP) / midPrice;
  
  return {
    bidVWAP,
    askVWAP,
    imbalanceRatio,
    timestamp: Date.now(),
  };
}

// VWAP Calculation Helper
function calculateVWAP(levels: [number, number][]): number {
  let totalValue = 0;
  let totalVolume = 0;
  
  for (const [price, amount] of levels) {
    totalValue += price * amount;
    totalVolume += amount;
  }
  
  return totalVolume > 0 ? totalValue / totalVolume : 0;
}

// CVD Rate of Change (Momentum) Calculation
export function calculateCVDMomentum(cvdHistory: CVDHistoryEntry[]): number {
  if (cvdHistory.length < 2) return 0;
  
  const [prev, curr] = cvdHistory.slice(-2);
  const timeDiff = (curr.timestamp - prev.timestamp) / (1000 * 60); // Minutes
  
  return timeDiff > 0 ? (curr.cvd - prev.cvd) / timeDiff : 0;
}

// Simple EMA Calculation (fallback if TAAPI is unavailable)
export function calculateEMA(values: number[], period: number): number {
  if (values.length === 0) return 0;
  if (values.length === 1) return values[0];
  
  const k = 2 / (period + 1);
  let ema = values[0];
  
  for (let i = 1; i < values.length; i++) {
    ema = ema * (1 - k) + values[i] * k;
  }
  
  return ema;
}

// Signal Generation Logic
export function generateTradingSignal(
  cvd: number,
  imbalance: number,
  momentum: number,
  price: number,
  thresholds: {
    cvdThreshold: number;
    imbalanceThreshold: number;
    momentumThreshold: number;
  }
): TradingSignal {
  const { cvdThreshold, imbalanceThreshold, momentumThreshold } = thresholds;
  
  const reasons: string[] = [];
  let signalType: 'buy' | 'sell' | 'hold' = 'hold';
  let strength = 0;
  let confidence = 0;
  
  // Bullish conditions
  const bullishCVD = cvd > cvdThreshold;
  const bullishImbalance = imbalance > imbalanceThreshold;
  const bullishMomentum = momentum > momentumThreshold;
  
  // Bearish conditions
  const bearishCVD = cvd < -cvdThreshold;
  const bearishImbalance = imbalance < -imbalanceThreshold;
  const bearishMomentum = momentum < -momentumThreshold;
  
  // Calculate bullish signal
  if (bullishCVD || bullishImbalance || bullishMomentum) {
    let bullishScore = 0;
    
    if (bullishCVD) {
      bullishScore += 40;
      reasons.push(`Positive CVD: ${cvd.toFixed(2)}`);
    }
    if (bullishImbalance) {
      bullishScore += 35;
      reasons.push(`Bid imbalance: ${(imbalance * 100).toFixed(2)}%`);
    }
    if (bullishMomentum) {
      bullishScore += 25;
      reasons.push(`Positive momentum: ${momentum.toFixed(2)}`);
    }
    
    if (bullishScore >= 60) {
      signalType = 'buy';
      strength = Math.min(bullishScore, 100);
      confidence = bullishScore / 100;
    }
  }
  
  // Calculate bearish signal
  if (bearishCVD || bearishImbalance || bearishMomentum) {
    let bearishScore = 0;
    
    if (bearishCVD) {
      bearishScore += 40;
      reasons.push(`Negative CVD: ${cvd.toFixed(2)}`);
    }
    if (bearishImbalance) {
      bearishScore += 35;
      reasons.push(`Ask imbalance: ${(imbalance * 100).toFixed(2)}%`);
    }
    if (bearishMomentum) {
      bearishScore += 25;
      reasons.push(`Negative momentum: ${momentum.toFixed(2)}`);
    }
    
    if (bearishScore >= 60) {
      signalType = 'sell';
      strength = Math.min(bearishScore, 100);
      confidence = bearishScore / 100;
    }
  }
  
  if (reasons.length === 0) {
    reasons.push('No significant signals detected');
  }
  
  return {
    type: signalType,
    strength,
    timestamp: Date.now(),
    reasons,
    confidence,
    metadata: {
      cvd,
      imbalance,
      momentum,
      price,
    },
  };
}

// Filter Recent Trades (last 15 minutes)
export function filterRecentTrades(trades: Trade[], windowMinutes: number = 15): Trade[] {
  const cutoffTime = Date.now() - (windowMinutes * 60 * 1000);
  return trades.filter(trade => trade.timestamp >= cutoffTime);
}

// Position Size Calculation
export function calculatePositionSize(
  accountBalance: number,
  riskPercentage: number,
  entryPrice: number,
  stopLossPrice: number,
  leverage: number
): number {
  const riskAmount = accountBalance * (riskPercentage / 100);
  const priceRisk = Math.abs(entryPrice - stopLossPrice) / entryPrice;
  const positionValue = riskAmount / priceRisk;
  
  return (positionValue / entryPrice) * leverage;
}

// Format Currency
export function formatCurrency(amount: number, decimals: number = 2): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount);
}

// Format Percentage
export function formatPercentage(value: number, decimals: number = 2): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

// Sleep utility
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Retry utility with exponential backoff
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      const delay = baseDelay * Math.pow(2, attempt);
      logger.warn(`Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error);
      await sleep(delay);
    }
  }
  
  throw lastError!;
}

// Validate trading symbol format
export function validateTradingSymbol(symbol: string): boolean {
  // Check for perpetual swap format: BASE/QUOTE:SETTLE
  const perpetualPattern = /^[A-Z]{3,5}\/[A-Z]{3,5}:[A-Z]{3,5}$/;
  return perpetualPattern.test(symbol);
}

// Calculate ATR (Average True Range) for stop loss
export function calculateATR(ohlcv: number[][], period: number = 14): number {
  if (ohlcv.length < period + 1) return 0;
  
  const trueRanges: number[] = [];
  
  for (let i = 1; i < ohlcv.length; i++) {
    const [, , high, low, close] = ohlcv[i];
    const prevClose = ohlcv[i - 1][4];
    
    const tr1 = high - low;
    const tr2 = Math.abs(high - prevClose);
    const tr3 = Math.abs(low - prevClose);
    
    trueRanges.push(Math.max(tr1, tr2, tr3));
  }
  
  return calculateEMA(trueRanges.slice(-period), period);
}

// Health check utility
export function createHealthCheck(name: string, checkFn: () => Promise<boolean>) {
  return {
    name,
    check: checkFn,
    lastCheck: 0,
    status: false,
  };
}

export default {
  logger,
  calculateCVD,
  detectOrderBookImbalance,
  calculateCVDMomentum,
  calculateEMA,
  generateTradingSignal,
  filterRecentTrades,
  calculatePositionSize,
  formatCurrency,
  formatPercentage,
  sleep,
  retryWithBackoff,
  validateTradingSymbol,
  calculateATR,
  createHealthCheck,
};
