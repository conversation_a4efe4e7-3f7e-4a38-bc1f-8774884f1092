# API Keys (Required to enable respective provider)
ANTHROPIC_API_KEY="your_anthropic_api_key_here"       # Required: Format: sk-ant-api03-...
PERPLEXITY_API_KEY="your_perplexity_api_key_here"     # Optional: Format: pplx-...
OPENAI_API_KEY="your_openai_api_key_here"             # Optional, for OpenAI/OpenRouter models. Format: sk-proj-...
GOOGLE_API_KEY="your_google_api_key_here"             # Optional, for Google Gemini models.
MISTRAL_API_KEY="your_mistral_key_here"               # Optional, for Mistral AI models.
XAI_API_KEY="YOUR_XAI_KEY_HERE"                       # Required, for Grok 3 AI models.
AZURE_OPENAI_API_KEY="your_azure_key_here"            # Optional, for Azure OpenAI models (requires endpoint in .taskmasterconfig).

# Trading System API Keys
PHEMEX_API_KEY="your_phemex_api_key_here"             # Required: Phemex exchange API key for live trading
PHEMEX_SECRET="your_phemex_secret_here"               # Required: Phemex exchange secret for live trading
PHEMEX_PASSPHRASE="your_phemex_passphrase_here"       # Optional: Phemex passphrase if required
TAAPI_API_KEY="your_taapi_api_key_here"               # Required: TAAPI.io API key for technical indicators
DISCORD_BOT_TOKEN="your_discord_bot_token_here"       # Required: Discord bot token for notifications
DISCORD_CHANNEL_ID="your_discord_channel_id_here"     # Required: Discord channel ID for trade notifications

# Trading Configuration
TRADING_SYMBOL="BTC/USD:USD"                          # Trading symbol for perpetual swaps
LEVERAGE="10"                                         # Leverage multiplier (default: 10x)
RISK_PERCENTAGE="1.5"                                 # Risk percentage per trade (default: 1.5%)
TIMEFRAME="15m"                                       # Trading timeframe (default: 15 minutes)
ENABLE_LIVE_TRADING="false"                           # Set to "true" to enable live trading (default: false for safety)

# System Configuration
LOG_LEVEL="info"                                      # Logging level: debug, info, warn, error
GRAPH_EXECUTION_INTERVAL="1000"                      # Graph execution interval in milliseconds (default: 1 second)
CVD_DECAY_FACTOR="0.001"                             # CVD exponential decay factor
ORDER_BOOK_LEVELS="20"                               # Number of order book levels to analyze
CVD_THRESHOLD="1000"                                 # CVD threshold for signal generation
IMBALANCE_THRESHOLD="0.05"                           # Order book imbalance threshold (5%)
MOMENTUM_THRESHOLD="50"                              # Momentum threshold for signal generation
OLLAMA_API_KEY="your_ollama_api_key_here"             # Optional: For remote Ollama servers that require authentication.