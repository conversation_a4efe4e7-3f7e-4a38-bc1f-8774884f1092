import { EventEmitter } from 'events';
import { TradingState, AgentInput, AgentOutput, TradingSignal, VWAPData, TradingConfig, TradingSystemError } from '@/types';
import { logger, detectOrderBookImbalance, calculateCVDMomentum, generateTradingSignal } from '@/utils';
import { CONSTANTS } from '@/config';

export class SignalGenerationAgent extends EventEmitter {
  private config: TradingConfig;
  private isActive: boolean = false;
  private lastSignalTimestamp: number = 0;
  private signalHistory: TradingSignal[] = [];

  constructor(config: TradingConfig) {
    super();
    this.config = config;

    logger.info('SignalGenerationAgent initialized', {
      cvdThreshold: this.config.cvdThreshold,
      imbalanceThreshold: this.config.imbalanceThreshold,
      momentumThreshold: this.config.momentumThreshold,
    });
  }

  // Initialize the agent
  async initialize(): Promise<void> {
    try {
      this.isActive = true;
      this.emit('initialized');
      
      logger.info('SignalGenerationAgent started successfully');
    } catch (error) {
      logger.error('Failed to initialize SignalGenerationAgent:', error);
      throw new TradingSystemError(
        'Failed to initialize SignalGenerationAgent',
        'SIGNAL_GENERATION_INIT_ERROR',
        'SignalGenerationAgent',
        error as Error
      );
    }
  }

  // Process agent input and generate signals
  async process(state: TradingState, input: AgentInput): Promise<{ state: TradingState; output: AgentOutput }> {
    try {
      const startTime = Date.now();
      let updated = false;
      let newSignals: TradingSignal[] = [];

      // Generate signals if we have sufficient data
      if (this.hasSufficientData(state)) {
        const signals = await this.generateSignals(state);
        
        if (signals.length > 0) {
          newSignals = signals;
          state = this.updateSignalsInState(state, signals);
          updated = true;
        }
      }

      const processingTime = Date.now() - startTime;

      const output: AgentOutput = {
        success: true,
        data: {
          updated,
          processingTime,
          signalsGenerated: newSignals.length,
          signals: newSignals,
          totalSignals: state.signals.length,
        },
        timestamp: Date.now(),
      };

      if (updated && newSignals.length > 0) {
        logger.info('SignalGenerationAgent generated signals', {
          count: newSignals.length,
          types: newSignals.map(s => s.type),
          strengths: newSignals.map(s => s.strength),
        });

        // Emit signal events
        newSignals.forEach(signal => {
          this.emit('signal_generated', signal);
        });
      }

      return { state, output };
    } catch (error) {
      logger.error('SignalGenerationAgent processing error:', error);
      
      const output: AgentOutput = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      };

      return { state, output };
    }
  }

  // Check if we have sufficient data for signal generation
  private hasSufficientData(state: TradingState): boolean {
    return !!(
      state.orderBook &&
      state.orderBook.bids.length >= this.config.orderBookLevels &&
      state.orderBook.asks.length >= this.config.orderBookLevels &&
      state.cvdHistory.length >= 2 &&
      state.recentTrades.length >= CONSTANTS.MIN_TRADES_FOR_CVD
    );
  }

  // Generate trading signals
  private async generateSignals(state: TradingState): Promise<TradingSignal[]> {
    const signals: TradingSignal[] = [];

    try {
      // Calculate order book imbalance using VWAP
      const vwapData = this.calculateOrderBookVWAP(state);
      
      // Update VWAP in state
      state.vwap = vwapData;
      state.imbalance = vwapData.imbalanceRatio;

      // Get latest CVD and calculate momentum
      const latestCVD = state.cvdHistory[state.cvdHistory.length - 1]?.cvd || 0;
      const momentum = calculateCVDMomentum(state.cvdHistory);

      // Get current price
      const currentPrice = state.orderBook?.bids[0]?.[0] || 0;

      // Generate primary signal
      const primarySignal = generateTradingSignal(
        latestCVD,
        vwapData.imbalanceRatio,
        momentum,
        currentPrice,
        {
          cvdThreshold: this.config.cvdThreshold,
          imbalanceThreshold: this.config.imbalanceThreshold,
          momentumThreshold: this.config.momentumThreshold,
        }
      );

      // Only add signal if it meets minimum confidence threshold
      if (primarySignal.confidence >= CONSTANTS.MIN_SIGNAL_CONFIDENCE) {
        signals.push(primarySignal);
      }

      // Generate additional confirmation signals
      const confirmationSignals = await this.generateConfirmationSignals(state, vwapData, latestCVD, momentum, currentPrice);
      signals.push(...confirmationSignals);

      // Filter and rank signals
      const filteredSignals = this.filterAndRankSignals(signals);

      return filteredSignals;
    } catch (error) {
      logger.error('Error generating signals:', error);
      return [];
    }
  }

  // Calculate order book VWAP and imbalance
  private calculateOrderBookVWAP(state: TradingState): VWAPData {
    if (!state.orderBook) {
      throw new Error('Order book data not available');
    }

    try {
      const vwapData = detectOrderBookImbalance(state.orderBook, this.config.orderBookLevels);
      
      logger.debug('Order book VWAP calculated', {
        bidVWAP: vwapData.bidVWAP,
        askVWAP: vwapData.askVWAP,
        imbalanceRatio: vwapData.imbalanceRatio,
      });

      return vwapData;
    } catch (error) {
      logger.error('Error calculating order book VWAP:', error);
      throw error;
    }
  }

  // Generate confirmation signals
  private async generateConfirmationSignals(
    state: TradingState,
    vwapData: VWAPData,
    cvd: number,
    momentum: number,
    price: number
  ): Promise<TradingSignal[]> {
    const confirmationSignals: TradingSignal[] = [];

    try {
      // Volume confirmation signal
      const volumeSignal = this.generateVolumeSignal(state, cvd, price);
      if (volumeSignal) {
        confirmationSignals.push(volumeSignal);
      }

      // Momentum confirmation signal
      const momentumSignal = this.generateMomentumSignal(state, momentum, price);
      if (momentumSignal) {
        confirmationSignals.push(momentumSignal);
      }

      // Order book depth signal
      const depthSignal = this.generateOrderBookDepthSignal(state, vwapData, price);
      if (depthSignal) {
        confirmationSignals.push(depthSignal);
      }

      // Trend confirmation signal
      const trendSignal = this.generateTrendSignal(state, price);
      if (trendSignal) {
        confirmationSignals.push(trendSignal);
      }

    } catch (error) {
      logger.error('Error generating confirmation signals:', error);
    }

    return confirmationSignals;
  }

  // Generate volume-based signal
  private generateVolumeSignal(state: TradingState, cvd: number, price: number): TradingSignal | null {
    if (state.recentTrades.length === 0) return null;

    const recentVolume = state.recentTrades.reduce((sum, trade) => sum + trade.amount, 0);
    const avgVolume = recentVolume / state.recentTrades.length;
    
    // Calculate volume surge
    const volumeSurge = recentVolume / (avgVolume * state.recentTrades.length);
    
    if (volumeSurge > 1.5) { // 50% above average
      const signalType = cvd > 0 ? 'buy' : 'sell';
      
      return {
        type: signalType,
        strength: Math.min(volumeSurge * 30, 100),
        timestamp: Date.now(),
        reasons: [`Volume surge detected: ${(volumeSurge * 100).toFixed(1)}%`],
        confidence: Math.min(volumeSurge * 0.3, 0.8),
        metadata: {
          cvd,
          imbalance: 0,
          momentum: 0,
          price,
        },
      };
    }

    return null;
  }

  // Generate momentum-based signal
  private generateMomentumSignal(state: TradingState, momentum: number, price: number): TradingSignal | null {
    const momentumThreshold = this.config.momentumThreshold;
    
    if (Math.abs(momentum) > momentumThreshold) {
      const signalType = momentum > 0 ? 'buy' : 'sell';
      const strength = Math.min((Math.abs(momentum) / momentumThreshold) * 60, 100);
      
      return {
        type: signalType,
        strength,
        timestamp: Date.now(),
        reasons: [`Strong momentum: ${momentum.toFixed(2)}`],
        confidence: Math.min(strength / 100, 0.7),
        metadata: {
          cvd: 0,
          imbalance: 0,
          momentum,
          price,
        },
      };
    }

    return null;
  }

  // Generate order book depth signal
  private generateOrderBookDepthSignal(state: TradingState, vwapData: VWAPData, price: number): TradingSignal | null {
    if (!state.orderBook) return null;

    const bidDepth = state.orderBook.bids.slice(0, 10).reduce((sum, [, amount]) => sum + amount, 0);
    const askDepth = state.orderBook.asks.slice(0, 10).reduce((sum, [, amount]) => sum + amount, 0);
    
    const depthRatio = bidDepth / (bidDepth + askDepth);
    
    if (Math.abs(depthRatio - 0.5) > 0.15) { // 15% imbalance
      const signalType = depthRatio > 0.5 ? 'buy' : 'sell';
      const strength = Math.abs(depthRatio - 0.5) * 400; // Scale to 0-100
      
      return {
        type: signalType,
        strength,
        timestamp: Date.now(),
        reasons: [`Order book depth imbalance: ${(depthRatio * 100).toFixed(1)}% bids`],
        confidence: Math.min(strength / 100, 0.6),
        metadata: {
          cvd: 0,
          imbalance: vwapData.imbalanceRatio,
          momentum: 0,
          price,
        },
      };
    }

    return null;
  }

  // Generate trend-based signal
  private generateTrendSignal(state: TradingState, price: number): TradingSignal | null {
    if (state.ohlcv.length < 3) return null;

    const recentCandles = state.ohlcv.slice(-3);
    const closes = recentCandles.map(candle => candle[4]); // Close prices
    
    // Simple trend detection
    const isUptrend = closes[2] > closes[1] && closes[1] > closes[0];
    const isDowntrend = closes[2] < closes[1] && closes[1] < closes[0];
    
    if (isUptrend || isDowntrend) {
      const signalType = isUptrend ? 'buy' : 'sell';
      const priceChange = Math.abs(closes[2] - closes[0]) / closes[0];
      const strength = Math.min(priceChange * 1000, 80); // Scale based on price change
      
      return {
        type: signalType,
        strength,
        timestamp: Date.now(),
        reasons: [`${isUptrend ? 'Uptrend' : 'Downtrend'} detected over 3 periods`],
        confidence: Math.min(strength / 100, 0.5),
        metadata: {
          cvd: 0,
          imbalance: 0,
          momentum: 0,
          price,
        },
      };
    }

    return null;
  }

  // Filter and rank signals by strength and confidence
  private filterAndRankSignals(signals: TradingSignal[]): TradingSignal[] {
    // Filter signals that meet minimum confidence
    const filteredSignals = signals.filter(signal => 
      signal.confidence >= CONSTANTS.MIN_SIGNAL_CONFIDENCE
    );

    // Sort by combined score (strength * confidence)
    filteredSignals.sort((a, b) => {
      const scoreA = a.strength * a.confidence;
      const scoreB = b.strength * b.confidence;
      return scoreB - scoreA;
    });

    // Return top 3 signals
    return filteredSignals.slice(0, 3);
  }

  // Update signals in state
  private updateSignalsInState(state: TradingState, newSignals: TradingSignal[]): TradingState {
    // Add new signals to history
    this.signalHistory.push(...newSignals);
    
    // Keep only recent signals (last hour)
    const cutoffTime = Date.now() - (60 * 60 * 1000);
    this.signalHistory = this.signalHistory.filter(signal => signal.timestamp >= cutoffTime);

    // Update last signal timestamp
    if (newSignals.length > 0) {
      this.lastSignalTimestamp = Math.max(...newSignals.map(s => s.timestamp));
    }

    return {
      ...state,
      signals: newSignals,
      lastSignalTimestamp: this.lastSignalTimestamp,
    };
  }

  // Get signal statistics
  getSignalStats() {
    const recentSignals = this.signalHistory.filter(s => s.timestamp >= Date.now() - (60 * 60 * 1000));
    
    const buySignals = recentSignals.filter(s => s.type === 'buy').length;
    const sellSignals = recentSignals.filter(s => s.type === 'sell').length;
    const holdSignals = recentSignals.filter(s => s.type === 'hold').length;
    
    const avgConfidence = recentSignals.length > 0 
      ? recentSignals.reduce((sum, s) => sum + s.confidence, 0) / recentSignals.length 
      : 0;

    return {
      totalSignals: recentSignals.length,
      buySignals,
      sellSignals,
      holdSignals,
      avgConfidence,
      lastSignal: this.lastSignalTimestamp,
    };
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const signalFreshness = Date.now() - this.lastSignalTimestamp < 300000; // Within last 5 minutes
      return this.isActive && (this.lastSignalTimestamp === 0 || signalFreshness);
    } catch (error) {
      logger.error('SignalGenerationAgent health check failed:', error);
      return false;
    }
  }

  // Get agent status
  getStatus() {
    return {
      isActive: this.isActive,
      lastSignal: this.lastSignalTimestamp,
      signalStats: this.getSignalStats(),
    };
  }

  // Stop the agent
  async stop(): Promise<void> {
    try {
      this.isActive = false;
      this.removeAllListeners();
      
      logger.info('SignalGenerationAgent stopped');
    } catch (error) {
      logger.error('Error stopping SignalGenerationAgent:', error);
    }
  }
}
