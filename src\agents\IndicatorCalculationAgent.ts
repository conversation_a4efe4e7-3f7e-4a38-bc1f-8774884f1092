import { EventEmitter } from 'events';
import { TradingState, AgentInput, AgentOutput, CVDHistoryEntry, TradingConfig, TradingSystemError } from '@/types';
import { TaapiService } from '@/services/TaapiService';
import { logger, calculateCVD, calculateEMA, filterRecentTrades } from '@/utils';
import { CONSTANTS } from '@/config';

export class IndicatorCalculationAgent extends EventEmitter {
  private taapiService: TaapiService;
  private config: TradingConfig;
  private isActive: boolean = false;
  private lastCalculationTimestamp: number = 0;

  constructor(taapiService: TaapiService, config: TradingConfig) {
    super();
    this.taapiService = taapiService;
    this.config = config;

    logger.info('IndicatorCalculationAgent initialized', {
      cvdDecayFactor: this.config.cvdDecayFactor,
      emaPeriodsForCVD: this.config.emaPeriodsForCVD,
    });
  }

  // Initialize the agent
  async initialize(): Promise<void> {
    try {
      this.isActive = true;
      this.emit('initialized');
      
      logger.info('IndicatorCalculationAgent started successfully');
    } catch (error) {
      logger.error('Failed to initialize IndicatorCalculationAgent:', error);
      throw new TradingSystemError(
        'Failed to initialize IndicatorCalculationAgent',
        'INDICATOR_CALCULATION_INIT_ERROR',
        'IndicatorCalculationAgent',
        error as Error
      );
    }
  }

  // Process agent input and calculate indicators
  async process(state: TradingState, input: AgentInput): Promise<{ state: TradingState; output: AgentOutput }> {
    try {
      const startTime = Date.now();
      let updated = false;

      // Only process if we have new trades or this is a timer trigger
      if (input.newTrades || input.trigger === 'timer') {
        // Calculate CVD if we have recent trades
        if (state.recentTrades.length > 0) {
          const newState = await this.calculateIndicators(state);
          state = newState;
          updated = true;
        }
      }

      const processingTime = Date.now() - startTime;
      this.lastCalculationTimestamp = Date.now();

      const output: AgentOutput = {
        success: true,
        data: {
          updated,
          processingTime,
          cvdHistoryLength: state.cvdHistory.length,
          latestCVD: state.cvdHistory.length > 0 ? state.cvdHistory[state.cvdHistory.length - 1].cvd : 0,
          ema: state.ema,
        },
        timestamp: Date.now(),
      };

      if (updated) {
        logger.debug('IndicatorCalculationAgent processed indicators', output.data);
        this.emit('indicators_updated', {
          cvd: output.data.latestCVD,
          ema: state.ema,
          timestamp: Date.now(),
        });
      }

      return { state, output };
    } catch (error) {
      logger.error('IndicatorCalculationAgent processing error:', error);
      
      const output: AgentOutput = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      };

      return { state, output };
    }
  }

  // Calculate all indicators
  private async calculateIndicators(state: TradingState): Promise<TradingState> {
    try {
      // Calculate CVD with exponential decay
      const cvd = this.calculateCVDWithDecay(state.recentTrades);
      
      // Create new CVD history entry
      const cvdEntry: CVDHistoryEntry = {
        timestamp: Date.now(),
        cvd,
        volume: this.calculateTotalVolume(state.recentTrades),
        buyVolume: this.calculateBuyVolume(state.recentTrades),
        sellVolume: this.calculateSellVolume(state.recentTrades),
      };

      // Update CVD history
      const updatedCVDHistory = this.updateCVDHistory(state.cvdHistory, cvdEntry);

      // Calculate EMA of CVD
      const ema = await this.calculateCVDEMA(updatedCVDHistory);

      return {
        ...state,
        cvdHistory: updatedCVDHistory,
        ema,
      };
    } catch (error) {
      logger.error('Error calculating indicators:', error);
      throw error;
    }
  }

  // Calculate CVD with exponential decay
  private calculateCVDWithDecay(trades: any[]): number {
    if (trades.length === 0) return 0;

    const now = Date.now();
    const decayFactor = this.config.cvdDecayFactor;
    let weightedCVD = 0;
    let totalWeight = 0;

    for (const trade of trades) {
      const timeDiff = (now - trade.timestamp) / (1000 * 60); // Minutes
      const weight = Math.exp(-decayFactor * timeDiff);
      const volume = trade.side === 'buy' ? trade.amount : -trade.amount;
      
      weightedCVD += volume * weight;
      totalWeight += weight;
    }

    return totalWeight > 0 ? weightedCVD / totalWeight : 0;
  }

  // Calculate total volume
  private calculateTotalVolume(trades: any[]): number {
    return trades.reduce((sum, trade) => sum + trade.amount, 0);
  }

  // Calculate buy volume
  private calculateBuyVolume(trades: any[]): number {
    return trades
      .filter(trade => trade.side === 'buy')
      .reduce((sum, trade) => sum + trade.amount, 0);
  }

  // Calculate sell volume
  private calculateSellVolume(trades: any[]): number {
    return trades
      .filter(trade => trade.side === 'sell')
      .reduce((sum, trade) => sum + trade.amount, 0);
  }

  // Update CVD history with new entry
  private updateCVDHistory(currentHistory: CVDHistoryEntry[], newEntry: CVDHistoryEntry): CVDHistoryEntry[] {
    const updatedHistory = [...currentHistory, newEntry];
    
    // Filter to keep only recent entries (last 15 minutes)
    const cutoffTime = Date.now() - CONSTANTS.FIFTEEN_MINUTES;
    const recentHistory = updatedHistory.filter(entry => entry.timestamp >= cutoffTime);
    
    // Limit the number of entries to prevent memory issues
    return recentHistory.slice(-this.config.maxCVDHistory);
  }

  // Calculate EMA of CVD using TAAPI or local calculation
  private async calculateCVDEMA(cvdHistory: CVDHistoryEntry[]): Promise<number> {
    if (cvdHistory.length === 0) return 0;

    const cvdValues = cvdHistory.map(entry => entry.cvd);
    
    try {
      // Try to use TAAPI for EMA calculation
      const ema = await this.taapiService.calculateEMA(
        cvdValues,
        this.config.emaPeriodsForCVD
      );
      
      logger.debug('EMA calculated via TAAPI', { ema, periods: this.config.emaPeriodsForCVD });
      return ema;
    } catch (error) {
      logger.warn('TAAPI EMA calculation failed, using local calculation:', error);
      
      // Fallback to local EMA calculation
      const localEMA = calculateEMA(cvdValues, this.config.emaPeriodsForCVD);
      logger.debug('EMA calculated locally', { ema: localEMA, periods: this.config.emaPeriodsForCVD });
      
      return localEMA;
    }
  }

  // Calculate additional indicators for enhanced analysis
  async calculateAdditionalIndicators(state: TradingState): Promise<{
    vwap?: number;
    atr?: number;
    rsi?: number;
  }> {
    const indicators: any = {};

    try {
      // Calculate VWAP using TAAPI
      try {
        indicators.vwap = await this.taapiService.calculateVWAP(
          'phemex',
          this.convertSymbolForTaapi(this.config.symbol),
          this.config.timeframe
        );
      } catch (error) {
        logger.debug('VWAP calculation failed:', error);
      }

      // Calculate ATR for stop-loss calculation
      try {
        indicators.atr = await this.taapiService.calculateATR(
          14, // 14-period ATR
          'phemex',
          this.convertSymbolForTaapi(this.config.symbol),
          this.config.timeframe
        );
      } catch (error) {
        logger.debug('ATR calculation failed:', error);
      }

      // Calculate RSI for additional confirmation
      try {
        indicators.rsi = await this.taapiService.calculateRSI(
          14, // 14-period RSI
          'phemex',
          this.convertSymbolForTaapi(this.config.symbol),
          this.config.timeframe
        );
      } catch (error) {
        logger.debug('RSI calculation failed:', error);
      }

      logger.debug('Additional indicators calculated', indicators);
    } catch (error) {
      logger.error('Error calculating additional indicators:', error);
    }

    return indicators;
  }

  // Calculate batch indicators for efficiency
  async calculateBatchIndicators(): Promise<{ [key: string]: number }> {
    try {
      const indicators = [
        { name: 'vwap', params: {} },
        { name: 'atr', params: { period: 14 } },
        { name: 'rsi', params: { period: 14 } },
        { name: 'ema', params: { period: 20 } }, // 20-period EMA for trend
      ];

      const results = await this.taapiService.calculateBatchIndicators(
        indicators,
        'phemex',
        this.convertSymbolForTaapi(this.config.symbol),
        this.config.timeframe
      );

      logger.debug('Batch indicators calculated', results);
      return results;
    } catch (error) {
      logger.error('Batch indicators calculation failed:', error);
      return {};
    }
  }

  // Convert symbol format for TAAPI (BTC/USD:USD -> BTC/USDT)
  private convertSymbolForTaapi(symbol: string): string {
    // Convert perpetual swap symbol to spot symbol for TAAPI
    if (symbol.includes(':')) {
      const base = symbol.split('/')[0];
      return `${base}/USDT`;
    }
    return symbol;
  }

  // Calculate CVD momentum (rate of change)
  calculateCVDMomentum(cvdHistory: CVDHistoryEntry[]): number {
    if (cvdHistory.length < 2) return 0;

    const [prev, curr] = cvdHistory.slice(-2);
    const timeDiff = (curr.timestamp - prev.timestamp) / (1000 * 60); // Minutes
    
    return timeDiff > 0 ? (curr.cvd - prev.cvd) / timeDiff : 0;
  }

  // Calculate volume-weighted CVD
  calculateVolumeWeightedCVD(cvdHistory: CVDHistoryEntry[]): number {
    if (cvdHistory.length === 0) return 0;

    let weightedSum = 0;
    let totalVolume = 0;

    for (const entry of cvdHistory) {
      weightedSum += entry.cvd * entry.volume;
      totalVolume += entry.volume;
    }

    return totalVolume > 0 ? weightedSum / totalVolume : 0;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const taapiHealth = await this.taapiService.healthCheck();
      const calculationFreshness = Date.now() - this.lastCalculationTimestamp < 120000; // Within last 2 minutes
      
      return this.isActive && taapiHealth && calculationFreshness;
    } catch (error) {
      logger.error('IndicatorCalculationAgent health check failed:', error);
      return false;
    }
  }

  // Get agent status
  getStatus() {
    return {
      isActive: this.isActive,
      lastCalculation: this.lastCalculationTimestamp,
      taapiUsage: this.taapiService.getUsageStats(),
    };
  }

  // Stop the agent
  async stop(): Promise<void> {
    try {
      this.isActive = false;
      this.removeAllListeners();
      
      logger.info('IndicatorCalculationAgent stopped');
    } catch (error) {
      logger.error('Error stopping IndicatorCalculationAgent:', error);
    }
  }
}
