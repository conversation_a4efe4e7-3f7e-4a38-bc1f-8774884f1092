import { EventEmitter } from 'events';
import { TradingState, AgentInput, AgentOutput, TradeEvent, TradingSignal, TradingConfig, TradingSystemError } from '@/types';
import { DiscordService } from '@/services/DiscordService';
import { logger, formatCurrency, formatPercentage } from '@/utils';

export class NotificationAgent extends EventEmitter {
  private discordService: DiscordService;
  private config: TradingConfig;
  private isActive: boolean = false;
  private lastNotificationTimestamp: number = 0;
  private notificationHistory: any[] = [];
  private notificationSettings = {
    sendSignals: true,
    sendTrades: true,
    sendPositionUpdates: true,
    sendDailySummary: true,
    signalThreshold: 0.7, // Only send signals with confidence >= 70%
  };

  constructor(discordService: DiscordService, config: TradingConfig) {
    super();
    this.discordService = discordService;
    this.config = config;

    logger.info('NotificationAgent initialized', {
      enableLiveTrading: this.config.enableLiveTrading,
    });
  }

  // Initialize the agent
  async initialize(): Promise<void> {
    try {
      // Initialize Discord service
      await this.discordService.initialize();
      
      this.isActive = true;
      this.emit('initialized');
      
      logger.info('NotificationAgent started successfully');
    } catch (error) {
      logger.error('Failed to initialize NotificationAgent:', error);
      throw new TradingSystemError(
        'Failed to initialize NotificationAgent',
        'NOTIFICATION_INIT_ERROR',
        'NotificationAgent',
        error as Error
      );
    }
  }

  // Process agent input and send notifications
  async process(state: TradingState, input: AgentInput): Promise<{ state: TradingState; output: AgentOutput }> {
    try {
      const startTime = Date.now();
      let updated = false;
      let notificationsSent = 0;

      // Send trade event notifications
      if (state.tradeEvents.length > 0) {
        const newEvents = this.getNewTradeEvents(state.tradeEvents);
        for (const event of newEvents) {
          if (this.notificationSettings.sendTrades) {
            await this.sendTradeNotification(event);
            notificationsSent++;
          }
        }
        updated = newEvents.length > 0;
      }

      // Send signal notifications
      if (state.signals.length > 0) {
        const strongSignals = state.signals.filter(s => 
          s.confidence >= this.notificationSettings.signalThreshold
        );
        
        for (const signal of strongSignals) {
          if (this.notificationSettings.sendSignals && this.shouldSendSignalNotification(signal)) {
            await this.sendSignalNotification(signal);
            notificationsSent++;
          }
        }
        updated = updated || strongSignals.length > 0;
      }

      // Send position updates
      if (state.activePositions.length > 0 && this.notificationSettings.sendPositionUpdates) {
        const positionUpdates = await this.checkPositionUpdates(state);
        notificationsSent += positionUpdates;
        updated = updated || positionUpdates > 0;
      }

      // Send system status updates
      const systemUpdates = await this.sendSystemUpdates(state);
      notificationsSent += systemUpdates;
      updated = updated || systemUpdates > 0;

      const processingTime = Date.now() - startTime;
      this.lastNotificationTimestamp = Date.now();

      const output: AgentOutput = {
        success: true,
        data: {
          updated,
          processingTime,
          notificationsSent,
          discordConnected: this.discordService.connected,
        },
        timestamp: Date.now(),
      };

      if (notificationsSent > 0) {
        logger.debug('NotificationAgent sent notifications', {
          count: notificationsSent,
        });
      }

      return { state, output };
    } catch (error) {
      logger.error('NotificationAgent processing error:', error);
      
      const output: AgentOutput = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      };

      return { state, output };
    }
  }

  // Get new trade events that haven't been notified
  private getNewTradeEvents(tradeEvents: TradeEvent[]): TradeEvent[] {
    const lastNotifiedTimestamp = this.getLastNotifiedEventTimestamp();
    return tradeEvents.filter(event => event.timestamp > lastNotifiedTimestamp);
  }

  // Send trade event notification
  private async sendTradeNotification(event: TradeEvent): Promise<void> {
    try {
      await this.discordService.sendTradeNotification(event);
      
      this.addToNotificationHistory({
        type: 'trade',
        event,
        timestamp: Date.now(),
      });

      logger.info('Trade notification sent', {
        type: event.type,
        symbol: event.symbol,
        side: event.side,
      });
    } catch (error) {
      logger.error('Failed to send trade notification:', error);
    }
  }

  // Send signal notification
  private async sendSignalNotification(signal: TradingSignal): Promise<void> {
    try {
      await this.discordService.sendSignalNotification(signal);
      
      this.addToNotificationHistory({
        type: 'signal',
        signal,
        timestamp: Date.now(),
      });

      logger.info('Signal notification sent', {
        type: signal.type,
        strength: signal.strength,
        confidence: signal.confidence,
      });
    } catch (error) {
      logger.error('Failed to send signal notification:', error);
    }
  }

  // Check if we should send signal notification
  private shouldSendSignalNotification(signal: TradingSignal): boolean {
    // Don't send duplicate signals within 5 minutes
    const recentSignalNotifications = this.notificationHistory.filter(n => 
      n.type === 'signal' && 
      n.timestamp >= Date.now() - (5 * 60 * 1000) &&
      n.signal.type === signal.type
    );

    return recentSignalNotifications.length === 0;
  }

  // Check for position updates that need notification
  private async checkPositionUpdates(state: TradingState): Promise<number> {
    let notificationsSent = 0;

    try {
      for (const position of state.activePositions) {
        // Check for significant P&L changes
        if (this.shouldNotifyPositionUpdate(position)) {
          await this.discordService.sendPositionUpdate(position);
          notificationsSent++;
          
          this.addToNotificationHistory({
            type: 'position_update',
            position,
            timestamp: Date.now(),
          });
        }
      }
    } catch (error) {
      logger.error('Error checking position updates:', error);
    }

    return notificationsSent;
  }

  // Check if position update should be sent
  private shouldNotifyPositionUpdate(position: any): boolean {
    // Notify if unrealized P&L is significant (> 5% of position value)
    const positionValue = position.size * position.entryPrice;
    const pnlPercentage = Math.abs(position.unrealizedPnL) / positionValue;
    
    return pnlPercentage > 0.05; // 5% threshold
  }

  // Send system status updates
  private async sendSystemUpdates(state: TradingState): Promise<number> {
    let notificationsSent = 0;

    try {
      // Send connection status updates
      if (this.shouldSendConnectionUpdate(state)) {
        await this.sendConnectionStatusUpdate(state);
        notificationsSent++;
      }

      // Send error notifications
      if (state.errorCount > 0) {
        await this.sendErrorNotification(state);
        notificationsSent++;
      }

      // Send daily summary (once per day)
      if (this.shouldSendDailySummary()) {
        await this.sendDailySummary(state);
        notificationsSent++;
      }

    } catch (error) {
      logger.error('Error sending system updates:', error);
    }

    return notificationsSent;
  }

  // Check if connection status update should be sent
  private shouldSendConnectionUpdate(state: TradingState): boolean {
    const lastConnectionUpdate = this.getLastNotificationOfType('connection_status');
    const timeSinceLastUpdate = Date.now() - (lastConnectionUpdate?.timestamp || 0);
    
    // Send update every 30 minutes or if connection status changed
    return timeSinceLastUpdate > (30 * 60 * 1000);
  }

  // Send connection status update
  private async sendConnectionStatusUpdate(state: TradingState): Promise<void> {
    try {
      const status = state.connectionStatus;
      const allConnected = Object.values(status).every(connected => connected);
      
      const title = allConnected ? '✅ All Systems Connected' : '⚠️ Connection Issues Detected';
      const color = allConnected ? 'GREEN' : 'YELLOW';
      
      const statusText = Object.entries(status)
        .map(([service, connected]) => `${service}: ${connected ? '✅' : '❌'}`)
        .join('\n');

      await this.discordService.sendSystemNotification(
        title,
        `**Connection Status:**\n${statusText}`,
        color
      );

      this.addToNotificationHistory({
        type: 'connection_status',
        status,
        timestamp: Date.now(),
      });

    } catch (error) {
      logger.error('Failed to send connection status update:', error);
    }
  }

  // Send error notification
  private async sendErrorNotification(state: TradingState): Promise<void> {
    try {
      await this.discordService.sendSystemNotification(
        '🚨 System Errors Detected',
        `The trading system has encountered ${state.errorCount} errors. Please check the logs for details.`,
        'RED'
      );

      this.addToNotificationHistory({
        type: 'error',
        errorCount: state.errorCount,
        timestamp: Date.now(),
      });

    } catch (error) {
      logger.error('Failed to send error notification:', error);
    }
  }

  // Check if daily summary should be sent
  private shouldSendDailySummary(): boolean {
    if (!this.notificationSettings.sendDailySummary) return false;
    
    const lastSummary = this.getLastNotificationOfType('daily_summary');
    const now = new Date();
    const lastSummaryDate = lastSummary ? new Date(lastSummary.timestamp) : null;
    
    // Send summary once per day at market close (or if never sent)
    return !lastSummaryDate || 
           lastSummaryDate.getDate() !== now.getDate() ||
           lastSummaryDate.getMonth() !== now.getMonth();
  }

  // Send daily summary
  private async sendDailySummary(state: TradingState): Promise<void> {
    try {
      const summary = this.calculateDailySummary(state);
      await this.discordService.sendDailySummary(summary);

      this.addToNotificationHistory({
        type: 'daily_summary',
        summary,
        timestamp: Date.now(),
      });

    } catch (error) {
      logger.error('Failed to send daily summary:', error);
    }
  }

  // Calculate daily summary statistics
  private calculateDailySummary(state: TradingState) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();

    const todayEvents = state.tradeEvents.filter(e => e.timestamp >= todayTimestamp);
    const closedTrades = todayEvents.filter(e => e.type === 'position_closed');
    
    const totalTrades = todayEvents.filter(e => e.type === 'position_opened').length;
    const winningTrades = closedTrades.filter(e => (e.pnl || 0) > 0).length;
    const winRate = totalTrades > 0 ? winningTrades / totalTrades : 0;
    
    const totalPnL = closedTrades.reduce((sum, e) => sum + (e.pnl || 0), 0);
    const bestTrade = Math.max(...closedTrades.map(e => e.pnl || 0), 0);
    const worstTrade = Math.min(...closedTrades.map(e => e.pnl || 0), 0);

    return {
      totalTrades,
      winRate,
      totalPnL,
      bestTrade,
      worstTrade,
      activePositions: state.activePositions.length,
    };
  }

  // Add notification to history
  private addToNotificationHistory(notification: any): void {
    this.notificationHistory.push(notification);
    
    // Keep only last 1000 notifications
    if (this.notificationHistory.length > 1000) {
      this.notificationHistory = this.notificationHistory.slice(-1000);
    }
  }

  // Get last notified event timestamp
  private getLastNotifiedEventTimestamp(): number {
    const tradeNotifications = this.notificationHistory.filter(n => n.type === 'trade');
    return tradeNotifications.length > 0 
      ? Math.max(...tradeNotifications.map(n => n.event.timestamp))
      : 0;
  }

  // Get last notification of specific type
  private getLastNotificationOfType(type: string): any {
    const notifications = this.notificationHistory.filter(n => n.type === type);
    return notifications.length > 0 ? notifications[notifications.length - 1] : null;
  }

  // Update notification settings
  updateSettings(settings: Partial<typeof this.notificationSettings>): void {
    this.notificationSettings = { ...this.notificationSettings, ...settings };
    logger.info('Notification settings updated', this.notificationSettings);
  }

  // Get notification statistics
  getNotificationStats() {
    const recentNotifications = this.notificationHistory.filter(n => 
      n.timestamp >= Date.now() - (24 * 60 * 60 * 1000) // Last 24 hours
    );

    const typeCounts = recentNotifications.reduce((counts, notification) => {
      counts[notification.type] = (counts[notification.type] || 0) + 1;
      return counts;
    }, {} as { [key: string]: number });

    return {
      totalNotifications: recentNotifications.length,
      typeCounts,
      lastNotification: this.lastNotificationTimestamp,
      discordConnected: this.discordService.connected,
      settings: this.notificationSettings,
    };
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const discordHealth = await this.discordService.healthCheck();
      return this.isActive && discordHealth;
    } catch (error) {
      logger.error('NotificationAgent health check failed:', error);
      return false;
    }
  }

  // Get agent status
  getStatus() {
    return {
      isActive: this.isActive,
      lastNotification: this.lastNotificationTimestamp,
      notificationStats: this.getNotificationStats(),
    };
  }

  // Stop the agent
  async stop(): Promise<void> {
    try {
      this.isActive = false;
      await this.discordService.disconnect();
      this.removeAllListeners();
      
      logger.info('NotificationAgent stopped');
    } catch (error) {
      logger.error('Error stopping NotificationAgent:', error);
    }
  }
}
