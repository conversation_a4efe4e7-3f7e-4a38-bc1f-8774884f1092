import { StateGraph, END } from '@langchain/langgraph';
import { EventEmitter } from 'events';
import * as cron from 'node-cron';
import { TradingState, AgentInput, ConnectionStatus, TradingConfig, APIConfig } from '@/types';
import config, { validateConfig, getEnvironmentInfo } from '@/config';
import { logger, createHealthCheck } from '@/utils';

// Services
import { PhemexService } from '@/services/PhemexService';
import { GrokService } from '@/services/GrokService';
import { TaapiService } from '@/services/TaapiService';
import { DiscordService } from '@/services/DiscordService';

// Agents
import { DataCollectionAgent } from '@/agents/DataCollectionAgent';
import { IndicatorCalculationAgent } from '@/agents/IndicatorCalculationAgent';
import { SignalGenerationAgent } from '@/agents/SignalGenerationAgent';
import { DecisionMakingAgent } from '@/agents/DecisionMakingAgent';
import { OrderExecutionAgent } from '@/agents/OrderExecutionAgent';
import { NotificationAgent } from '@/agents/NotificationAgent';

class TradingSystem extends EventEmitter {
  private graph: StateGraph<TradingState>;
  private state: TradingState;
  private isRunning: boolean = false;
  private executionInterval: NodeJS.Timeout | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  // Services
  private phemexService: PhemexService;
  private grokService: GrokService;
  private taapiService: TaapiService;
  private discordService: DiscordService;

  // Agents
  private dataCollectionAgent: DataCollectionAgent;
  private indicatorCalculationAgent: IndicatorCalculationAgent;
  private signalGenerationAgent: SignalGenerationAgent;
  private decisionMakingAgent: DecisionMakingAgent;
  private orderExecutionAgent: OrderExecutionAgent;
  private notificationAgent: NotificationAgent;

  constructor() {
    super();
    
    // Initialize services
    this.phemexService = new PhemexService(config.api.phemex, config.trading);
    this.grokService = new GrokService(config.api.grok);
    this.taapiService = new TaapiService(config.api.taapi);
    this.discordService = new DiscordService(config.api.discord);

    // Initialize agents
    this.dataCollectionAgent = new DataCollectionAgent(this.phemexService, config.trading);
    this.indicatorCalculationAgent = new IndicatorCalculationAgent(this.taapiService, config.trading);
    this.signalGenerationAgent = new SignalGenerationAgent(config.trading);
    this.decisionMakingAgent = new DecisionMakingAgent(this.grokService, this.phemexService, config.trading);
    this.orderExecutionAgent = new OrderExecutionAgent(this.phemexService, config.trading);
    this.notificationAgent = new NotificationAgent(this.discordService, config.trading);

    // Initialize state
    this.state = this.createInitialState();

    // Create LangGraph workflow
    this.graph = this.createWorkflow();

    this.setupEventHandlers();
    
    logger.info('TradingSystem initialized', getEnvironmentInfo());
  }

  // Create initial trading state
  private createInitialState(): TradingState {
    return {
      recentTrades: [],
      orderBook: null,
      ohlcv: [],
      cvdHistory: [],
      ema: 0,
      imbalance: 0,
      vwap: {
        bidVWAP: 0,
        askVWAP: 0,
        imbalanceRatio: 0,
        timestamp: 0,
      },
      signals: [],
      lastSignalTimestamp: 0,
      decision: null,
      activePositions: [],
      tradeEvents: [],
      lastUpdateTimestamp: 0,
      connectionStatus: {
        phemex: false,
        discord: false,
        taapi: false,
        grok: false,
        lastHealthCheck: 0,
      },
      errorCount: 0,
    };
  }

  // Create LangGraph workflow
  private createWorkflow(): StateGraph<TradingState> {
    const workflow = new StateGraph<TradingState>({
      channels: {
        state: {
          reducer: (left: TradingState, right: Partial<TradingState>) => ({
            ...left,
            ...right,
          }),
        },
      },
    });

    // Add nodes (agents)
    workflow.addNode('dataCollection', async (state: TradingState) => {
      const input: AgentInput = { trigger: 'timer' };
      const result = await this.dataCollectionAgent.process(state, input);
      return result.state;
    });

    workflow.addNode('indicatorCalculation', async (state: TradingState) => {
      const input: AgentInput = { trigger: 'timer' };
      const result = await this.indicatorCalculationAgent.process(state, input);
      return result.state;
    });

    workflow.addNode('signalGeneration', async (state: TradingState) => {
      const input: AgentInput = { trigger: 'timer' };
      const result = await this.signalGenerationAgent.process(state, input);
      return result.state;
    });

    workflow.addNode('decisionMaking', async (state: TradingState) => {
      const input: AgentInput = { trigger: 'timer' };
      const result = await this.decisionMakingAgent.process(state, input);
      return result.state;
    });

    workflow.addNode('orderExecution', async (state: TradingState) => {
      const input: AgentInput = { trigger: 'timer' };
      const result = await this.orderExecutionAgent.process(state, input);
      return result.state;
    });

    workflow.addNode('notification', async (state: TradingState) => {
      const input: AgentInput = { trigger: 'timer' };
      const result = await this.notificationAgent.process(state, input);
      return result.state;
    });

    // Define workflow edges
    workflow.addEdge('dataCollection', 'indicatorCalculation');
    workflow.addEdge('indicatorCalculation', 'signalGeneration');
    workflow.addEdge('signalGeneration', 'decisionMaking');
    workflow.addEdge('decisionMaking', 'orderExecution');
    workflow.addEdge('orderExecution', 'notification');
    workflow.addEdge('notification', END);

    // Set entry point
    workflow.setEntryPoint('dataCollection');

    return workflow.compile();
  }

  // Setup event handlers
  private setupEventHandlers(): void {
    // Data collection events
    this.dataCollectionAgent.on('data_update', (data) => {
      this.handleDataUpdate(data);
    });

    // Signal generation events
    this.signalGenerationAgent.on('signal_generated', (signal) => {
      logger.info('New signal generated', {
        type: signal.type,
        strength: signal.strength,
        confidence: signal.confidence,
      });
    });

    // Decision making events
    this.decisionMakingAgent.on('decision_made', (decision) => {
      logger.info('Trade decision made', {
        action: decision.action,
        confidence: decision.confidence,
      });
    });

    // Order execution events
    this.orderExecutionAgent.on('trade_executed', (event) => {
      logger.info('Trade executed', {
        type: event.type,
        symbol: event.symbol,
        side: event.side,
        amount: event.amount,
      });
    });

    // Error handling
    this.on('error', (error) => {
      logger.error('System error:', error);
      this.state.errorCount++;
    });

    // Graceful shutdown
    process.on('SIGINT', () => this.shutdown());
    process.on('SIGTERM', () => this.shutdown());
  }

  // Handle real-time data updates
  private async handleDataUpdate(data: any): Promise<void> {
    try {
      // Create input for immediate graph execution
      const input: AgentInput = {
        newTrades: data.type === 'trades' ? data.data : undefined,
        newOrderBook: data.type === 'orderbook' ? data.data : undefined,
        newOHLCV: data.type === 'ohlcv' ? data.data : undefined,
        trigger: 'websocket',
      };

      // Execute graph with new data
      await this.executeGraph(input);
    } catch (error) {
      logger.error('Error handling data update:', error);
      this.emit('error', error);
    }
  }

  // Execute the LangGraph workflow
  private async executeGraph(input?: AgentInput): Promise<void> {
    try {
      const startTime = Date.now();
      
      // Execute the graph
      const result = await this.graph.invoke(this.state);
      
      // Update state
      this.state = result;
      
      const executionTime = Date.now() - startTime;
      
      logger.debug('Graph execution completed', {
        executionTime,
        signals: this.state.signals.length,
        activePositions: this.state.activePositions.length,
      });

    } catch (error) {
      logger.error('Graph execution error:', error);
      this.emit('error', error);
    }
  }

  // Initialize the trading system
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Multi-AI Agent Trading System...');

      // Validate configuration
      validateConfig();

      // Initialize all agents
      await this.dataCollectionAgent.initialize();
      await this.indicatorCalculationAgent.initialize();
      await this.signalGenerationAgent.initialize();
      await this.decisionMakingAgent.initialize();
      await this.orderExecutionAgent.initialize();
      await this.notificationAgent.initialize();

      logger.info('All agents initialized successfully');

      // Start periodic execution
      this.startPeriodicExecution();

      // Start health monitoring
      this.startHealthMonitoring();

      // Schedule daily tasks
      this.scheduleDailyTasks();

      this.isRunning = true;
      this.emit('initialized');

      logger.info('🚀 Multi-AI Agent Trading System is now running!');

    } catch (error) {
      logger.error('Failed to initialize trading system:', error);
      throw error;
    }
  }

  // Start periodic graph execution
  private startPeriodicExecution(): void {
    this.executionInterval = setInterval(async () => {
      if (this.isRunning) {
        await this.executeGraph();
      }
    }, config.trading.graphExecutionInterval);

    logger.info(`Periodic execution started (${config.trading.graphExecutionInterval}ms interval)`);
  }

  // Start health monitoring
  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, config.constants.HEALTH_CHECK_INTERVAL);

    logger.info('Health monitoring started');
  }

  // Perform system health check
  private async performHealthCheck(): Promise<void> {
    try {
      const healthChecks = {
        phemex: await this.phemexService.healthCheck(),
        discord: await this.discordService.healthCheck(),
        taapi: await this.taapiService.healthCheck(),
        grok: await this.grokService.healthCheck(),
      };

      this.state.connectionStatus = {
        ...healthChecks,
        lastHealthCheck: Date.now(),
      };

      const allHealthy = Object.values(healthChecks).every(healthy => healthy);
      
      if (!allHealthy) {
        logger.warn('Health check failed for some services', healthChecks);
      }

    } catch (error) {
      logger.error('Health check error:', error);
    }
  }

  // Schedule daily tasks
  private scheduleDailyTasks(): void {
    // Daily summary at 23:59
    cron.schedule('59 23 * * *', async () => {
      logger.info('Generating daily summary...');
      // Daily summary will be handled by notification agent
    });

    // Daily cleanup at 00:01
    cron.schedule('1 0 * * *', async () => {
      logger.info('Performing daily cleanup...');
      this.performDailyCleanup();
    });

    logger.info('Daily tasks scheduled');
  }

  // Perform daily cleanup
  private performDailyCleanup(): void {
    // Reset error count
    this.state.errorCount = 0;

    // Clean old trade events (keep last 7 days)
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    this.state.tradeEvents = this.state.tradeEvents.filter(
      event => event.timestamp >= sevenDaysAgo
    );

    logger.info('Daily cleanup completed');
  }

  // Get system status
  getStatus() {
    return {
      isRunning: this.isRunning,
      state: {
        lastUpdate: this.state.lastUpdateTimestamp,
        signals: this.state.signals.length,
        activePositions: this.state.activePositions.length,
        connectionStatus: this.state.connectionStatus,
        errorCount: this.state.errorCount,
      },
      agents: {
        dataCollection: this.dataCollectionAgent.getStatus(),
        indicatorCalculation: this.indicatorCalculationAgent.getStatus(),
        signalGeneration: this.signalGenerationAgent.getStatus(),
        decisionMaking: this.decisionMakingAgent.getStatus(),
        orderExecution: this.orderExecutionAgent.getStatus(),
        notification: this.notificationAgent.getStatus(),
      },
    };
  }

  // Shutdown the system
  async shutdown(): Promise<void> {
    try {
      logger.info('Shutting down Multi-AI Agent Trading System...');

      this.isRunning = false;

      // Clear intervals
      if (this.executionInterval) {
        clearInterval(this.executionInterval);
      }
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
      }

      // Stop all agents
      await Promise.all([
        this.dataCollectionAgent.stop(),
        this.indicatorCalculationAgent.stop(),
        this.signalGenerationAgent.stop(),
        this.decisionMakingAgent.stop(),
        this.orderExecutionAgent.stop(),
        this.notificationAgent.stop(),
      ]);

      this.emit('shutdown');
      logger.info('✅ Trading system shutdown completed');

      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }
}

// Main execution
async function main() {
  try {
    const tradingSystem = new TradingSystem();
    await tradingSystem.initialize();

    // Keep the process running
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      tradingSystem.shutdown();
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection at:', promise, 'reason:', reason);
      tradingSystem.shutdown();
    });

  } catch (error) {
    logger.error('Failed to start trading system:', error);
    process.exit(1);
  }
}

// Start the system
if (require.main === module) {
  main();
}

export default TradingSystem;
