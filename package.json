{"name": "multi-ai-agent-trading-system", "version": "1.0.0", "description": "Multi-AI Agent Trading System for Perpetual Swaps using TypeScript with Real-Time WebSocket Data and LangGraph JS Memory", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node scripts/start.js", "start:direct": "node dist/main.js", "dev": "ts-node src/main.ts", "dev:watch": "ts-node --watch src/main.ts", "watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "setup": "npm install && npm run build", "clean": "rimraf dist logs/*.log", "check": "npm run lint && npm run build && npm test"}, "keywords": ["trading", "ai", "agents", "typescript", "websocket", "phemex", "langgraph", "grok"], "author": "Trading System Developer", "license": "MIT", "dependencies": {"@langchain/langgraph": "^0.2.19", "@langchain/core": "^0.3.15", "ccxt": "^4.4.29", "discord.js": "^14.16.3", "axios": "^1.7.7", "ws": "^8.18.0", "dotenv": "^16.4.5", "winston": "^3.15.0", "node-cron": "^3.0.3", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^22.9.0", "@types/ws": "^8.5.13", "@types/uuid": "^10.0.0", "@types/node-cron": "^3.0.11", "typescript": "^5.6.3", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^8.13.0", "@typescript-eslint/parser": "^8.13.0", "eslint": "^9.14.0", "prettier": "^3.3.3", "jest": "^29.7.0", "@types/jest": "^29.5.14", "rimraf": "^6.0.1"}, "engines": {"node": ">=18.0.0"}}