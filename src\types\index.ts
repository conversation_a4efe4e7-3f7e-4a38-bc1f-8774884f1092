import { OrderBook, Trade, OHLCV, Order } from 'ccxt';

// Core Trading State Interface for LangGraph JS Memory
export interface TradingState {
  // Real-time data
  recentTrades: Trade[];
  orderBook: OrderBook | null;
  ohlcv: OHLCV[];
  
  // Calculated indicators
  cvdHistory: CVDHistoryEntry[];
  ema: number;
  imbalance: number;
  vwap: VWAPData;
  
  // Signal and decision data
  signals: TradingSignal[];
  lastSignalTimestamp: number;
  decision: TradeDecision | null;
  
  // Trade management
  activePositions: Position[];
  tradeEvents: TradeEvent[];
  
  // System state
  lastUpdateTimestamp: number;
  connectionStatus: ConnectionStatus;
  errorCount: number;
}

// CVD (Cumulative Volume Delta) History Entry
export interface CVDHistoryEntry {
  timestamp: number;
  cvd: number;
  volume: number;
  buyVolume: number;
  sellVolume: number;
}

// VWAP Data Structure
export interface VWAPData {
  bidVWAP: number;
  askVWAP: number;
  imbalanceRatio: number;
  timestamp: number;
}

// Trading Signal Types
export interface TradingSignal {
  type: 'buy' | 'sell' | 'hold';
  strength: number; // 0-100
  timestamp: number;
  reasons: string[];
  confidence: number; // 0-1
  metadata: {
    cvd: number;
    imbalance: number;
    momentum: number;
    price: number;
  };
}

// Trade Decision from Grok 3
export interface TradeDecision {
  action: 'enter_long' | 'enter_short' | 'close_position' | 'hold' | 'scale_out';
  symbol: string;
  side: 'buy' | 'sell';
  amount: number;
  price?: number;
  stopLoss?: number;
  takeProfit?: number;
  leverage: number;
  reasoning: string;
  confidence: number;
  riskReward: number;
  timestamp: number;
}

// Position Management
export interface Position {
  id: string;
  symbol: string;
  side: 'long' | 'short';
  size: number;
  entryPrice: number;
  currentPrice: number;
  unrealizedPnL: number;
  realizedPnL: number;
  leverage: number;
  stopLoss?: number;
  takeProfit?: number;
  openTimestamp: number;
  lastUpdateTimestamp: number;
  status: 'open' | 'closing' | 'closed';
}

// Trade Events for Notifications
export interface TradeEvent {
  id: string;
  type: 'position_opened' | 'position_closed' | 'stop_loss_hit' | 'take_profit_hit' | 'partial_close';
  positionId: string;
  symbol: string;
  side: 'buy' | 'sell';
  amount: number;
  price: number;
  pnl?: number;
  timestamp: number;
  message: string;
}

// Connection Status
export interface ConnectionStatus {
  phemex: boolean;
  discord: boolean;
  taapi: boolean;
  grok: boolean;
  lastHealthCheck: number;
}

// Agent Input/Output Types
export interface AgentInput {
  newTrades?: Trade[];
  newOrderBook?: OrderBook;
  newOHLCV?: OHLCV[];
  trigger?: 'websocket' | 'timer' | 'manual';
}

export interface AgentOutput {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: number;
}

// Configuration Types
export interface TradingConfig {
  symbol: string;
  leverage: number;
  riskPercentage: number;
  timeframe: string;
  enableLiveTrading: boolean;
  
  // Thresholds
  cvdThreshold: number;
  imbalanceThreshold: number;
  momentumThreshold: number;
  
  // Technical parameters
  cvdDecayFactor: number;
  orderBookLevels: number;
  emaPeriodsForCVD: number;
  
  // System parameters
  graphExecutionInterval: number;
  maxRecentTrades: number;
  maxCVDHistory: number;
}

// API Configuration
export interface APIConfig {
  phemex: {
    apiKey: string;
    secret: string;
    passphrase?: string;
    sandbox: boolean;
  };
  grok: {
    apiKey: string;
    baseURL?: string;
    model: string;
  };
  taapi: {
    apiKey: string;
    baseURL?: string;
  };
  discord: {
    token: string;
    channelId: string;
  };
}

// Grok 3 API Types
export interface GrokPrompt {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface GrokResponse {
  decision: TradeDecision;
  reasoning: string;
  confidence: number;
  riskAssessment: string;
}

// TAAPI Response Types
export interface TaapiEMAResponse {
  value: number;
  timestamp: number;
}

export interface TaapiVWAPResponse {
  value: number;
  timestamp: number;
}

// Error Types
export class TradingSystemError extends Error {
  constructor(
    message: string,
    public code: string,
    public agent?: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'TradingSystemError';
  }
}

// Utility Types
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: number;
  agent?: string;
  data?: any;
}

// WebSocket Event Types
export type WebSocketEventType = 'trades' | 'orderbook' | 'ohlcv' | 'ticker';

export interface WebSocketEvent {
  type: WebSocketEventType;
  symbol: string;
  data: any;
  timestamp: number;
}

// Risk Management Types
export interface RiskMetrics {
  currentDrawdown: number;
  maxDrawdown: number;
  winRate: number;
  profitFactor: number;
  sharpeRatio: number;
  totalTrades: number;
  totalPnL: number;
  lastCalculated: number;
}

export interface RiskLimits {
  maxPositionSize: number;
  maxDailyLoss: number;
  maxOpenPositions: number;
  maxLeverage: number;
  stopTradingDrawdown: number;
}
