import dotenv from 'dotenv';
import { TradingConfig, APIConfig, LogLevel } from '@/types';

// Load environment variables
dotenv.config();

// Validate required environment variables
const requiredEnvVars = [
  'XAI_API_KEY',
  'PHEMEX_API_KEY', 
  'PHEMEX_SECRET',
  'TAAPI_API_KEY',
  'DISCORD_BOT_TOKEN',
  'DISCORD_CHANNEL_ID'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
}

// Trading Configuration
export const tradingConfig: TradingConfig = {
  symbol: process.env.TRADING_SYMBOL || 'BTC/USD:USD',
  leverage: parseInt(process.env.LEVERAGE || '10'),
  riskPercentage: parseFloat(process.env.RISK_PERCENTAGE || '1.5'),
  timeframe: process.env.TIMEFRAME || '15m',
  enableLiveTrading: process.env.ENABLE_LIVE_TRADING === 'true',
  
  // Signal thresholds
  cvdThreshold: parseFloat(process.env.CVD_THRESHOLD || '1000'),
  imbalanceThreshold: parseFloat(process.env.IMBALANCE_THRESHOLD || '0.05'),
  momentumThreshold: parseFloat(process.env.MOMENTUM_THRESHOLD || '50'),
  
  // Technical parameters
  cvdDecayFactor: parseFloat(process.env.CVD_DECAY_FACTOR || '0.001'),
  orderBookLevels: parseInt(process.env.ORDER_BOOK_LEVELS || '20'),
  emaPeriodsForCVD: 5, // Fixed as per requirements
  
  // System parameters
  graphExecutionInterval: parseInt(process.env.GRAPH_EXECUTION_INTERVAL || '1000'),
  maxRecentTrades: 1000, // Keep last 1000 trades for CVD calculation
  maxCVDHistory: 100, // Keep last 100 CVD entries
};

// API Configuration
export const apiConfig: APIConfig = {
  phemex: {
    apiKey: process.env.PHEMEX_API_KEY!,
    secret: process.env.PHEMEX_SECRET!,
    passphrase: process.env.PHEMEX_PASSPHRASE,
    sandbox: !tradingConfig.enableLiveTrading, // Use sandbox when live trading is disabled
  },
  grok: {
    apiKey: process.env.XAI_API_KEY!,
    baseURL: process.env.GROK_BASE_URL || 'https://api.x.ai/v1',
    model: process.env.GROK_MODEL || 'grok-3-mini',
  },
  taapi: {
    apiKey: process.env.TAAPI_API_KEY!,
    baseURL: process.env.TAAPI_BASE_URL || 'https://api.taapi.io',
  },
  discord: {
    token: process.env.DISCORD_BOT_TOKEN!,
    channelId: process.env.DISCORD_CHANNEL_ID!,
  },
};

// Logging Configuration
export const logLevel: LogLevel = (process.env.LOG_LEVEL as LogLevel) || 'info';

// System Constants
export const CONSTANTS = {
  // Time constants (in milliseconds)
  FIFTEEN_MINUTES: 15 * 60 * 1000,
  ONE_MINUTE: 60 * 1000,
  ONE_SECOND: 1000,
  
  // WebSocket reconnection
  RECONNECT_DELAY: 5000,
  MAX_RECONNECT_ATTEMPTS: 10,
  
  // Rate limiting
  API_RATE_LIMIT_DELAY: 100,
  TAAPI_RATE_LIMIT: 1000, // 1 request per second for free tier
  
  // Risk management
  MAX_POSITION_SIZE_MULTIPLIER: 0.1, // 10% of account balance
  EMERGENCY_STOP_DRAWDOWN: 0.05, // 5% account drawdown
  
  // Signal validation
  MIN_SIGNAL_CONFIDENCE: 0.6,
  MIN_TRADE_CONFIDENCE: 0.7,
  
  // Order book analysis
  MIN_ORDER_BOOK_DEPTH: 10,
  MAX_SPREAD_PERCENTAGE: 0.001, // 0.1%
  
  // CVD calculation
  CVD_WINDOW_MINUTES: 15,
  MIN_TRADES_FOR_CVD: 10,
  
  // Position management
  PARTIAL_CLOSE_PERCENTAGE: 0.5, // Close 50% at first target
  TRAILING_STOP_DISTANCE: 0.002, // 0.2% trailing stop
  
  // Health check intervals
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  CONNECTION_TIMEOUT: 10000, // 10 seconds
};

// Validation functions
export function validateConfig(): void {
  // Validate trading configuration
  if (tradingConfig.leverage < 1 || tradingConfig.leverage > 100) {
    throw new Error('Leverage must be between 1 and 100');
  }
  
  if (tradingConfig.riskPercentage < 0.1 || tradingConfig.riskPercentage > 10) {
    throw new Error('Risk percentage must be between 0.1% and 10%');
  }
  
  if (tradingConfig.cvdThreshold <= 0) {
    throw new Error('CVD threshold must be positive');
  }
  
  if (tradingConfig.imbalanceThreshold <= 0 || tradingConfig.imbalanceThreshold >= 1) {
    throw new Error('Imbalance threshold must be between 0 and 1');
  }
  
  if (tradingConfig.orderBookLevels < 5 || tradingConfig.orderBookLevels > 50) {
    throw new Error('Order book levels must be between 5 and 50');
  }
  
  // Validate API keys format
  if (!apiConfig.grok.apiKey.startsWith('xai-')) {
    console.warn('Warning: XAI API key format may be incorrect');
  }
  
  if (!apiConfig.discord.channelId.match(/^\d{17,19}$/)) {
    throw new Error('Discord channel ID must be a valid snowflake ID');
  }
  
  console.log('Configuration validation passed');
}

// Environment info
export function getEnvironmentInfo() {
  return {
    nodeVersion: process.version,
    platform: process.platform,
    environment: process.env.NODE_ENV || 'development',
    liveTrading: tradingConfig.enableLiveTrading,
    symbol: tradingConfig.symbol,
    leverage: tradingConfig.leverage,
    riskPercentage: tradingConfig.riskPercentage,
    logLevel,
    timestamp: new Date().toISOString(),
  };
}

// Export default configuration object
export default {
  trading: tradingConfig,
  api: apiConfig,
  logLevel,
  constants: CONSTANTS,
  validate: validateConfig,
  getEnvironmentInfo,
};
