# Multi-AI Agent Trading System

A sophisticated multi-AI agent trading system for perpetual swap contracts using TypeScript, real-time WebSocket data, and LangGraph JS for orchestration.

## 🚀 Features

- **Multi-Agent Architecture**: Six specialized AI agents working in coordination
- **Real-Time Data**: WebSocket streams from Phemex exchange for order book, trades, and OHLCV data
- **AI Decision Making**: Grok 3 integration for intelligent trade decisions
- **Advanced Indicators**: CVD calculation with exponential decay, EMA smoothing via TAAPI
- **Risk Management**: 10X leverage with proper position sizing and stop-loss management
- **Discord Notifications**: Real-time trade alerts and system status updates
- **State Management**: LangGraph JS for workflow orchestration and persistent memory

## 🏗️ System Architecture

### Agents
1. **Data Collection Agent**: Subscribes to real-time WebSocket streams
2. **Indicator Calculation Agent**: Computes CVD and technical indicators
3. **Signal Generation Agent**: Analyzes market data for trade signals
4. **Decision Making Agent**: Uses Grok 3 for trade decisions
5. **Order Execution Agent**: Manages trade execution with 10X leverage
6. **Notification Agent**: Sends updates via Discord

### Data Flow
```
Data Collection → Indicator Calculation → Signal Generation → Decision Making → Order Execution → Notification
```

## 📋 Prerequisites

- Node.js 18+ 
- TypeScript 5+
- API Keys for:
  - Phemex (live trading)
  - xAI (Grok 3)
  - TAAPI.io (technical indicators)
  - Discord (notifications)

## 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd multi-ai-agent-trading-system
```

2. **Install dependencies**
```bash
npm install
```

3. **Configure environment variables**
```bash
cp .env.example .env
```

Edit `.env` with your API keys:
```env
# Required API Keys
XAI_API_KEY="your_grok_api_key_here"
PHEMEX_API_KEY="your_phemex_api_key_here"
PHEMEX_SECRET="your_phemex_secret_here"
TAAPI_API_KEY="your_taapi_api_key_here"
DISCORD_BOT_TOKEN="your_discord_bot_token_here"
DISCORD_CHANNEL_ID="your_discord_channel_id_here"

# Trading Configuration
TRADING_SYMBOL="BTC/USD:USD"
LEVERAGE="10"
RISK_PERCENTAGE="1.5"
ENABLE_LIVE_TRADING="false"  # Set to "true" for live trading
```

4. **Build the project**
```bash
npm run build
```

## 🚦 Usage

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Watch Mode (for development)
```bash
npm run watch
```

## ⚙️ Configuration

### Trading Parameters
- **Symbol**: BTC/USD:USD (perpetual swap)
- **Leverage**: 10X (configurable)
- **Risk**: 1-2% per trade
- **Timeframe**: 15 minutes
- **CVD Decay Factor**: 0.001 (exponential decay)
- **Order Book Levels**: 20 (for VWAP analysis)

### Signal Thresholds
- **CVD Threshold**: 1000
- **Imbalance Threshold**: 5%
- **Momentum Threshold**: 50
- **Minimum Confidence**: 70%

## 🔒 Security

- All API keys stored in environment variables
- Sandbox mode available for testing
- Live trading disabled by default
- Position size limits and risk management

## 📊 Monitoring

### Discord Notifications
- Trade executions (open/close)
- Signal alerts
- Position updates
- Daily P&L summaries
- System status updates

### Logging
- Structured logging with Winston
- Multiple log levels (debug, info, warn, error)
- Separate error and combined log files

## 🧪 Testing

```bash
npm test
```

## 📈 Trading Strategy

### Entry Rules
- **Long**: Positive CVD trend + bid imbalance + bullish momentum
- **Short**: Negative CVD trend + ask imbalance + bearish momentum

### Risk Management
- 10X leverage with 1-2% account risk per trade
- Stop-loss at 1-2 ATR from entry
- Take-profit with 50% scale-out at target

### Exit Conditions
- Imbalance reversal
- CVD trend reversal
- Target not reached within 3-5 bars

## 🔧 Development

### Project Structure
```
src/
├── agents/           # AI agents
├── services/         # External service integrations
├── types/           # TypeScript interfaces
├── utils/           # Utility functions
├── config/          # Configuration management
└── main.ts          # Main orchestration file
```

### Adding New Agents
1. Create agent class extending EventEmitter
2. Implement `initialize()`, `process()`, and `stop()` methods
3. Add to workflow in `main.ts`

### Extending Indicators
1. Add calculation logic to `IndicatorCalculationAgent`
2. Update state interfaces in `types/index.ts`
3. Integrate with signal generation

## 🚨 Important Notes

### Live Trading Warnings
- **Test thoroughly** in sandbox mode before enabling live trading
- **Start with small amounts** to validate system behavior
- **Monitor closely** during initial live trading sessions
- **Have emergency stop procedures** in place

### Risk Disclaimers
- Trading cryptocurrencies involves substantial risk
- Past performance does not guarantee future results
- Only trade with funds you can afford to lose
- This system is for educational/research purposes

## 📝 Logs

Logs are stored in:
- `logs/error.log` - Error messages only
- `logs/combined.log` - All log messages

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For issues and questions:
1. Check the logs for error messages
2. Verify API key configuration
3. Ensure all services are accessible
4. Review Discord bot permissions

## 🔄 Updates

The system includes automatic health checks and reconnection logic for:
- WebSocket connections
- API service availability
- Discord bot connectivity

## 📚 Documentation

For detailed documentation on each component:
- See inline code comments
- Review agent-specific README files
- Check service integration guides

---

**⚠️ Disclaimer**: This trading system is provided for educational and research purposes only. Use at your own risk. The authors are not responsible for any financial losses incurred through the use of this system.
