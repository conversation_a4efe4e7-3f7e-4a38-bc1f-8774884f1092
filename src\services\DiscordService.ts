import { Client, GatewayIntentBits, TextChannel, EmbedBuilder, ColorResolvable } from 'discord.js';
import { APIConfig, TradeEvent, Position, TradingSignal, TradingSystemError } from '@/types';
import { logger, formatCurrency, formatPercentage } from '@/utils';

export class DiscordService {
  private client: Client;
  private config: APIConfig['discord'];
  private isConnected: boolean = false;
  private channel: TextChannel | null = null;

  constructor(apiConfig: APIConfig['discord']) {
    this.config = apiConfig;
    
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
      ],
    });

    this.setupEventHandlers();
    
    logger.info('DiscordService initialized');
  }

  // Initialize Discord connection
  async initialize(): Promise<void> {
    try {
      await this.client.login(this.config.token);
      
      // Wait for ready event
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Discord connection timeout'));
        }, 10000);

        this.client.once('ready', () => {
          clearTimeout(timeout);
          resolve();
        });
      });

      // Get the notification channel
      this.channel = await this.client.channels.fetch(this.config.channelId) as TextChannel;
      
      if (!this.channel) {
        throw new Error(`Channel ${this.config.channelId} not found`);
      }

      this.isConnected = true;
      
      // Send startup notification
      await this.sendSystemNotification('🚀 Trading System Started', 'Multi-AI Agent Trading System is now online and monitoring markets.', 'GREEN');
      
      logger.info('Discord service connected successfully');
    } catch (error) {
      logger.error('Failed to initialize Discord service:', error);
      throw new TradingSystemError(
        'Failed to initialize Discord service',
        'DISCORD_INIT_ERROR',
        'DiscordService',
        error as Error
      );
    }
  }

  // Setup event handlers
  private setupEventHandlers(): void {
    this.client.on('ready', () => {
      logger.info(`Discord bot logged in as ${this.client.user?.tag}`);
    });

    this.client.on('error', (error) => {
      logger.error('Discord client error:', error);
      this.isConnected = false;
    });

    this.client.on('disconnect', () => {
      logger.warn('Discord client disconnected');
      this.isConnected = false;
    });

    this.client.on('reconnecting', () => {
      logger.info('Discord client reconnecting...');
    });
  }

  // Send trade event notification
  async sendTradeNotification(event: TradeEvent): Promise<void> {
    if (!this.isConnected || !this.channel) {
      logger.warn('Discord not connected, skipping notification');
      return;
    }

    try {
      const embed = this.createTradeEmbed(event);
      await this.channel.send({ embeds: [embed] });
      
      logger.info('Trade notification sent', {
        type: event.type,
        symbol: event.symbol,
        side: event.side,
      });
    } catch (error) {
      logger.error('Failed to send trade notification:', error);
    }
  }

  // Send signal notification
  async sendSignalNotification(signal: TradingSignal): Promise<void> {
    if (!this.isConnected || !this.channel) {
      logger.warn('Discord not connected, skipping signal notification');
      return;
    }

    try {
      const embed = this.createSignalEmbed(signal);
      await this.channel.send({ embeds: [embed] });
      
      logger.debug('Signal notification sent', {
        type: signal.type,
        strength: signal.strength,
      });
    } catch (error) {
      logger.error('Failed to send signal notification:', error);
    }
  }

  // Send position update
  async sendPositionUpdate(position: Position): Promise<void> {
    if (!this.isConnected || !this.channel) {
      logger.warn('Discord not connected, skipping position update');
      return;
    }

    try {
      const embed = this.createPositionEmbed(position);
      await this.channel.send({ embeds: [embed] });
      
      logger.info('Position update sent', {
        symbol: position.symbol,
        side: position.side,
        pnl: position.unrealizedPnL,
      });
    } catch (error) {
      logger.error('Failed to send position update:', error);
    }
  }

  // Send system notification
  async sendSystemNotification(title: string, message: string, color: ColorResolvable = 'BLUE'): Promise<void> {
    if (!this.isConnected || !this.channel) {
      logger.warn('Discord not connected, skipping system notification');
      return;
    }

    try {
      const embed = new EmbedBuilder()
        .setTitle(title)
        .setDescription(message)
        .setColor(color)
        .setTimestamp()
        .setFooter({ text: 'Multi-AI Trading System' });

      await this.channel.send({ embeds: [embed] });
      
      logger.info('System notification sent', { title });
    } catch (error) {
      logger.error('Failed to send system notification:', error);
    }
  }

  // Send daily summary
  async sendDailySummary(summary: {
    totalTrades: number;
    winRate: number;
    totalPnL: number;
    bestTrade: number;
    worstTrade: number;
    activePositions: number;
  }): Promise<void> {
    if (!this.isConnected || !this.channel) {
      logger.warn('Discord not connected, skipping daily summary');
      return;
    }

    try {
      const embed = new EmbedBuilder()
        .setTitle('📊 Daily Trading Summary')
        .setColor(summary.totalPnL >= 0 ? 'GREEN' : 'RED')
        .addFields([
          { name: '📈 Total Trades', value: summary.totalTrades.toString(), inline: true },
          { name: '🎯 Win Rate', value: formatPercentage(summary.winRate), inline: true },
          { name: '💰 Total P&L', value: formatCurrency(summary.totalPnL), inline: true },
          { name: '🏆 Best Trade', value: formatCurrency(summary.bestTrade), inline: true },
          { name: '📉 Worst Trade', value: formatCurrency(summary.worstTrade), inline: true },
          { name: '📊 Active Positions', value: summary.activePositions.toString(), inline: true },
        ])
        .setTimestamp()
        .setFooter({ text: 'Daily Summary Report' });

      await this.channel.send({ embeds: [embed] });
      
      logger.info('Daily summary sent');
    } catch (error) {
      logger.error('Failed to send daily summary:', error);
    }
  }

  // Create trade event embed
  private createTradeEmbed(event: TradeEvent): EmbedBuilder {
    const isProfit = event.pnl ? event.pnl > 0 : false;
    const color: ColorResolvable = event.type === 'position_opened' ? 'BLUE' : 
                                  isProfit ? 'GREEN' : 'RED';

    const embed = new EmbedBuilder()
      .setTitle(this.getTradeEventTitle(event.type))
      .setColor(color)
      .addFields([
        { name: '📊 Symbol', value: event.symbol, inline: true },
        { name: '📈 Side', value: event.side.toUpperCase(), inline: true },
        { name: '💎 Amount', value: event.amount.toFixed(4), inline: true },
        { name: '💰 Price', value: formatCurrency(event.price), inline: true },
      ])
      .setTimestamp(event.timestamp)
      .setFooter({ text: `Trade ID: ${event.id}` });

    if (event.pnl !== undefined) {
      embed.addFields([
        { name: '💵 P&L', value: formatCurrency(event.pnl), inline: true },
      ]);
    }

    if (event.message) {
      embed.setDescription(event.message);
    }

    return embed;
  }

  // Create signal embed
  private createSignalEmbed(signal: TradingSignal): EmbedBuilder {
    const color: ColorResolvable = signal.type === 'buy' ? 'GREEN' : 
                                  signal.type === 'sell' ? 'RED' : 'YELLOW';

    const embed = new EmbedBuilder()
      .setTitle(`🔔 ${signal.type.toUpperCase()} Signal`)
      .setColor(color)
      .addFields([
        { name: '💪 Strength', value: `${signal.strength}/100`, inline: true },
        { name: '🎯 Confidence', value: formatPercentage(signal.confidence), inline: true },
        { name: '💰 Price', value: formatCurrency(signal.metadata.price), inline: true },
        { name: '📊 CVD', value: signal.metadata.cvd.toFixed(2), inline: true },
        { name: '⚖️ Imbalance', value: formatPercentage(signal.metadata.imbalance), inline: true },
        { name: '🚀 Momentum', value: signal.metadata.momentum.toFixed(2), inline: true },
      ])
      .setDescription(`**Reasons:** ${signal.reasons.join(', ')}`)
      .setTimestamp(signal.timestamp)
      .setFooter({ text: 'Signal Analysis' });

    return embed;
  }

  // Create position embed
  private createPositionEmbed(position: Position): EmbedBuilder {
    const isProfit = position.unrealizedPnL > 0;
    const color: ColorResolvable = isProfit ? 'GREEN' : 'RED';

    const embed = new EmbedBuilder()
      .setTitle(`📊 Position Update - ${position.symbol}`)
      .setColor(color)
      .addFields([
        { name: '📈 Side', value: position.side.toUpperCase(), inline: true },
        { name: '💎 Size', value: position.size.toFixed(4), inline: true },
        { name: '💰 Entry Price', value: formatCurrency(position.entryPrice), inline: true },
        { name: '📊 Current Price', value: formatCurrency(position.currentPrice), inline: true },
        { name: '💵 Unrealized P&L', value: formatCurrency(position.unrealizedPnL), inline: true },
        { name: '🔢 Leverage', value: `${position.leverage}x`, inline: true },
      ])
      .setTimestamp(position.lastUpdateTimestamp)
      .setFooter({ text: `Position ID: ${position.id}` });

    if (position.stopLoss) {
      embed.addFields([
        { name: '🛑 Stop Loss', value: formatCurrency(position.stopLoss), inline: true },
      ]);
    }

    if (position.takeProfit) {
      embed.addFields([
        { name: '🎯 Take Profit', value: formatCurrency(position.takeProfit), inline: true },
      ]);
    }

    return embed;
  }

  // Get trade event title
  private getTradeEventTitle(type: TradeEvent['type']): string {
    const titles = {
      'position_opened': '🚀 Position Opened',
      'position_closed': '🏁 Position Closed',
      'stop_loss_hit': '🛑 Stop Loss Hit',
      'take_profit_hit': '🎯 Take Profit Hit',
      'partial_close': '📊 Partial Close',
    };

    return titles[type] || '📊 Trade Event';
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    return this.isConnected && this.client.isReady();
  }

  // Disconnect
  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.sendSystemNotification('🛑 Trading System Stopped', 'Multi-AI Agent Trading System is shutting down.', 'RED');
      }
      
      this.client.destroy();
      this.isConnected = false;
      
      logger.info('Discord service disconnected');
    } catch (error) {
      logger.error('Error during Discord disconnect:', error);
    }
  }

  // Getters
  get connected(): boolean {
    return this.isConnected;
  }

  get channelId(): string {
    return this.config.channelId;
  }
}
