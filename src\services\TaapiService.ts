import axios, { AxiosInstance } from 'axios';
import { APIConfig, TaapiEMAResponse, TaapiVWAPResponse, TradingSystemError } from '@/types';
import { logger, retryWithBackoff, sleep } from '@/utils';
import { CONSTANTS } from '@/config';

export class TaapiService {
  private client: AxiosInstance;
  private config: APIConfig['taapi'];
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  private rateLimitDelay: number = CONSTANTS.TAAPI_RATE_LIMIT;

  constructor(apiConfig: APIConfig['taapi']) {
    this.config = apiConfig;
    
    this.client = axios.create({
      baseURL: this.config.baseURL,
      timeout: 15000, // 15 second timeout
      params: {
        secret: this.config.apiKey,
      },
    });

    logger.info('TaapiService initialized', {
      baseURL: this.config.baseURL,
    });
  }

  // Calculate 5-period EMA for CVD smoothing
  async calculateEMA(
    values: number[],
    period: number = 5,
    exchange: string = 'phemex',
    symbol: string = 'BTC/USDT',
    interval: string = '15m'
  ): Promise<number> {
    try {
      // Rate limiting
      await this.enforceRateLimit();

      // If we have enough local values, calculate EMA locally for better performance
      if (values.length >= period) {
        return this.calculateLocalEMA(values, period);
      }

      // Otherwise, use TAAPI for more comprehensive calculation
      const response = await retryWithBackoff(async () => {
        return await this.client.get('/ema', {
          params: {
            exchange,
            symbol,
            interval,
            period,
            backtrack: 0,
          },
        });
      });

      this.updateRequestStats();

      if (response.data && typeof response.data.value === 'number') {
        logger.debug('EMA calculated via TAAPI', {
          period,
          value: response.data.value,
        });
        return response.data.value;
      }

      throw new Error('Invalid EMA response from TAAPI');
    } catch (error) {
      logger.warn('TAAPI EMA calculation failed, using local calculation:', error);
      
      // Fallback to local calculation
      if (values.length > 0) {
        return this.calculateLocalEMA(values, period);
      }
      
      throw new TradingSystemError(
        'Failed to calculate EMA',
        'EMA_CALCULATION_ERROR',
        'TaapiService',
        error as Error
      );
    }
  }

  // Calculate VWAP for 15-minute timeframe
  async calculateVWAP(
    exchange: string = 'phemex',
    symbol: string = 'BTC/USDT',
    interval: string = '15m'
  ): Promise<number> {
    try {
      // Rate limiting
      await this.enforceRateLimit();

      const response = await retryWithBackoff(async () => {
        return await this.client.get('/vwap', {
          params: {
            exchange,
            symbol,
            interval,
            backtrack: 0,
          },
        });
      });

      this.updateRequestStats();

      if (response.data && typeof response.data.value === 'number') {
        logger.debug('VWAP calculated via TAAPI', {
          symbol,
          interval,
          value: response.data.value,
        });
        return response.data.value;
      }

      throw new Error('Invalid VWAP response from TAAPI');
    } catch (error) {
      logger.error('TAAPI VWAP calculation failed:', error);
      throw new TradingSystemError(
        'Failed to calculate VWAP',
        'VWAP_CALCULATION_ERROR',
        'TaapiService',
        error as Error
      );
    }
  }

  // Calculate ATR (Average True Range) for stop-loss calculation
  async calculateATR(
    period: number = 14,
    exchange: string = 'phemex',
    symbol: string = 'BTC/USDT',
    interval: string = '15m'
  ): Promise<number> {
    try {
      // Rate limiting
      await this.enforceRateLimit();

      const response = await retryWithBackoff(async () => {
        return await this.client.get('/atr', {
          params: {
            exchange,
            symbol,
            interval,
            period,
            backtrack: 0,
          },
        });
      });

      this.updateRequestStats();

      if (response.data && typeof response.data.value === 'number') {
        logger.debug('ATR calculated via TAAPI', {
          period,
          value: response.data.value,
        });
        return response.data.value;
      }

      throw new Error('Invalid ATR response from TAAPI');
    } catch (error) {
      logger.error('TAAPI ATR calculation failed:', error);
      throw new TradingSystemError(
        'Failed to calculate ATR',
        'ATR_CALCULATION_ERROR',
        'TaapiService',
        error as Error
      );
    }
  }

  // Calculate RSI for additional confirmation
  async calculateRSI(
    period: number = 14,
    exchange: string = 'phemex',
    symbol: string = 'BTC/USDT',
    interval: string = '15m'
  ): Promise<number> {
    try {
      // Rate limiting
      await this.enforceRateLimit();

      const response = await retryWithBackoff(async () => {
        return await this.client.get('/rsi', {
          params: {
            exchange,
            symbol,
            interval,
            period,
            backtrack: 0,
          },
        });
      });

      this.updateRequestStats();

      if (response.data && typeof response.data.value === 'number') {
        logger.debug('RSI calculated via TAAPI', {
          period,
          value: response.data.value,
        });
        return response.data.value;
      }

      throw new Error('Invalid RSI response from TAAPI');
    } catch (error) {
      logger.error('TAAPI RSI calculation failed:', error);
      throw new TradingSystemError(
        'Failed to calculate RSI',
        'RSI_CALCULATION_ERROR',
        'TaapiService',
        error as Error
      );
    }
  }

  // Calculate multiple indicators in a single batch request
  async calculateBatchIndicators(
    indicators: {
      name: string;
      params: any;
    }[],
    exchange: string = 'phemex',
    symbol: string = 'BTC/USDT',
    interval: string = '15m'
  ): Promise<{ [key: string]: number }> {
    try {
      // Rate limiting
      await this.enforceRateLimit();

      const requests = indicators.map(indicator => ({
        indicator: indicator.name,
        ...indicator.params,
        exchange,
        symbol,
        interval,
      }));

      const response = await retryWithBackoff(async () => {
        return await this.client.post('/bulk', {
          construct: {
            exchange,
            symbol,
            interval,
            indicators: requests,
          },
        });
      });

      this.updateRequestStats();

      const results: { [key: string]: number } = {};
      
      if (response.data && response.data.data) {
        for (let i = 0; i < indicators.length; i++) {
          const indicatorData = response.data.data[i];
          if (indicatorData && typeof indicatorData.value === 'number') {
            results[indicators[i].name] = indicatorData.value;
          }
        }
      }

      logger.debug('Batch indicators calculated via TAAPI', {
        indicatorCount: Object.keys(results).length,
        indicators: Object.keys(results),
      });

      return results;
    } catch (error) {
      logger.error('TAAPI batch calculation failed:', error);
      throw new TradingSystemError(
        'Failed to calculate batch indicators',
        'BATCH_CALCULATION_ERROR',
        'TaapiService',
        error as Error
      );
    }
  }

  // Local EMA calculation (fallback)
  private calculateLocalEMA(values: number[], period: number): number {
    if (values.length === 0) return 0;
    if (values.length === 1) return values[0];
    
    const k = 2 / (period + 1);
    let ema = values[0];
    
    for (let i = 1; i < values.length; i++) {
      ema = ema * (1 - k) + values[i] * k;
    }
    
    return ema;
  }

  // Rate limiting enforcement
  private async enforceRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.rateLimitDelay) {
      const delay = this.rateLimitDelay - timeSinceLastRequest;
      await sleep(delay);
    }
  }

  // Update request statistics
  private updateRequestStats(): void {
    this.requestCount++;
    this.lastRequestTime = Date.now();
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/ping');
      return response.status === 200;
    } catch (error) {
      logger.error('TAAPI health check failed:', error);
      return false;
    }
  }

  // Get usage statistics
  getUsageStats() {
    return {
      requestCount: this.requestCount,
      lastRequestTime: this.lastRequestTime,
      rateLimitDelay: this.rateLimitDelay,
    };
  }

  // Update rate limit based on subscription tier
  setRateLimit(requestsPerSecond: number): void {
    this.rateLimitDelay = 1000 / requestsPerSecond;
    logger.info(`TAAPI rate limit updated to ${requestsPerSecond} requests/second`);
  }

  // Get account info (if available)
  async getAccountInfo(): Promise<any> {
    try {
      const response = await this.client.get('/account');
      return response.data;
    } catch (error) {
      logger.warn('Failed to get TAAPI account info:', error);
      return null;
    }
  }
}
