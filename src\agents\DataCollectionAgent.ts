import { EventEmitter } from 'events';
import { Trade, OrderBook, OHLCV } from 'ccxt';
import { TradingState, AgentInput, AgentOutput, TradingConfig, TradingSystemError } from '@/types';
import { PhemexService } from '@/services/PhemexService';
import { logger, filterRecentTrades } from '@/utils';
import { CONSTANTS } from '@/config';

export class DataCollectionAgent extends EventEmitter {
  private phemexService: PhemexService;
  private config: TradingConfig;
  private isActive: boolean = false;
  private lastUpdateTimestamp: number = 0;

  constructor(phemexService: PhemexService, config: TradingConfig) {
    super();
    this.phemexService = phemexService;
    this.config = config;

    this.setupEventHandlers();
    
    logger.info('DataCollectionAgent initialized', {
      symbol: this.config.symbol,
      timeframe: this.config.timeframe,
    });
  }

  // Initialize the agent and start data collection
  async initialize(): Promise<void> {
    try {
      // Initialize Phemex service
      await this.phemexService.initialize();

      // Subscribe to WebSocket streams
      await this.subscribeToStreams();

      this.isActive = true;
      this.emit('initialized');
      
      logger.info('DataCollectionAgent started successfully');
    } catch (error) {
      logger.error('Failed to initialize DataCollectionAgent:', error);
      throw new TradingSystemError(
        'Failed to initialize DataCollectionAgent',
        'DATA_COLLECTION_INIT_ERROR',
        'DataCollectionAgent',
        error as Error
      );
    }
  }

  // Setup event handlers for Phemex service
  private setupEventHandlers(): void {
    // Order book updates
    this.phemexService.on('orderbook', (data) => {
      this.handleOrderBookUpdate(data);
    });

    // Trade updates
    this.phemexService.on('trades', (data) => {
      this.handleTradesUpdate(data);
    });

    // OHLCV updates
    this.phemexService.on('ohlcv', (data) => {
      this.handleOHLCVUpdate(data);
    });

    // Connection events
    this.phemexService.on('connected', () => {
      logger.info('Phemex WebSocket connected');
      this.emit('connection_status', { phemex: true });
    });

    this.phemexService.on('disconnected', () => {
      logger.warn('Phemex WebSocket disconnected');
      this.emit('connection_status', { phemex: false });
    });

    this.phemexService.on('error', (error) => {
      logger.error('Phemex service error:', error);
      this.emit('error', error);
    });
  }

  // Subscribe to all required WebSocket streams
  private async subscribeToStreams(): Promise<void> {
    try {
      // Subscribe to order book (Level 2)
      await this.phemexService.subscribeToOrderBook(this.config.symbol);
      
      // Subscribe to trades
      await this.phemexService.subscribeToTrades(this.config.symbol);
      
      // Subscribe to OHLCV data
      await this.phemexService.subscribeToOHLCV(this.config.symbol, this.config.timeframe);
      
      logger.info('All WebSocket streams subscribed successfully');
    } catch (error) {
      logger.error('Failed to subscribe to WebSocket streams:', error);
      throw error;
    }
  }

  // Process agent input and update state
  async process(state: TradingState, input: AgentInput): Promise<{ state: TradingState; output: AgentOutput }> {
    try {
      const startTime = Date.now();
      let updated = false;

      // Process new trades if available
      if (input.newTrades && input.newTrades.length > 0) {
        state = this.updateTradesInState(state, input.newTrades);
        updated = true;
      }

      // Process new order book if available
      if (input.newOrderBook) {
        state = this.updateOrderBookInState(state, input.newOrderBook);
        updated = true;
      }

      // Process new OHLCV if available
      if (input.newOHLCV && input.newOHLCV.length > 0) {
        state = this.updateOHLCVInState(state, input.newOHLCV);
        updated = true;
      }

      // Update last update timestamp if any data was processed
      if (updated) {
        state.lastUpdateTimestamp = Date.now();
        this.lastUpdateTimestamp = state.lastUpdateTimestamp;
      }

      const processingTime = Date.now() - startTime;

      const output: AgentOutput = {
        success: true,
        data: {
          updated,
          processingTime,
          tradesCount: state.recentTrades.length,
          orderBookLevels: state.orderBook ? state.orderBook.bids.length + state.orderBook.asks.length : 0,
          ohlcvCount: state.ohlcv.length,
        },
        timestamp: Date.now(),
      };

      if (updated) {
        logger.debug('DataCollectionAgent processed data', output.data);
      }

      return { state, output };
    } catch (error) {
      logger.error('DataCollectionAgent processing error:', error);
      
      const output: AgentOutput = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      };

      return { state, output };
    }
  }

  // Handle order book updates
  private handleOrderBookUpdate(data: { symbol: string; data: OrderBook }): void {
    if (data.symbol !== this.config.symbol) return;

    try {
      // Validate order book data
      if (!this.validateOrderBook(data.data)) {
        logger.warn('Invalid order book data received');
        return;
      }

      // Emit update for graph processing
      this.emit('data_update', {
        type: 'orderbook',
        symbol: data.symbol,
        data: data.data,
        timestamp: Date.now(),
      });

      logger.debug('Order book updated', {
        symbol: data.symbol,
        bids: data.data.bids.length,
        asks: data.data.asks.length,
      });
    } catch (error) {
      logger.error('Error handling order book update:', error);
    }
  }

  // Handle trades updates
  private handleTradesUpdate(data: { symbol: string; data: Trade[] }): void {
    if (data.symbol !== this.config.symbol) return;

    try {
      // Filter and validate trades
      const validTrades = data.data.filter(trade => this.validateTrade(trade));
      
      if (validTrades.length === 0) return;

      // Emit update for graph processing
      this.emit('data_update', {
        type: 'trades',
        symbol: data.symbol,
        data: validTrades,
        timestamp: Date.now(),
      });

      logger.debug('Trades updated', {
        symbol: data.symbol,
        count: validTrades.length,
        volume: validTrades.reduce((sum, trade) => sum + trade.amount, 0),
      });
    } catch (error) {
      logger.error('Error handling trades update:', error);
    }
  }

  // Handle OHLCV updates
  private handleOHLCVUpdate(data: { symbol: string; timeframe: string; data: OHLCV[] }): void {
    if (data.symbol !== this.config.symbol || data.timeframe !== this.config.timeframe) return;

    try {
      // Validate OHLCV data
      const validOHLCV = data.data.filter(candle => this.validateOHLCV(candle));
      
      if (validOHLCV.length === 0) return;

      // Emit update for graph processing
      this.emit('data_update', {
        type: 'ohlcv',
        symbol: data.symbol,
        timeframe: data.timeframe,
        data: validOHLCV,
        timestamp: Date.now(),
      });

      logger.debug('OHLCV updated', {
        symbol: data.symbol,
        timeframe: data.timeframe,
        count: validOHLCV.length,
      });
    } catch (error) {
      logger.error('Error handling OHLCV update:', error);
    }
  }

  // Update trades in state
  private updateTradesInState(state: TradingState, newTrades: Trade[]): TradingState {
    // Add new trades to recent trades
    const allTrades = [...state.recentTrades, ...newTrades];
    
    // Filter to keep only recent trades (last 15 minutes)
    const recentTrades = filterRecentTrades(allTrades, CONSTANTS.CVD_WINDOW_MINUTES);
    
    // Limit the number of trades to prevent memory issues
    const limitedTrades = recentTrades.slice(-this.config.maxRecentTrades);

    return {
      ...state,
      recentTrades: limitedTrades,
    };
  }

  // Update order book in state
  private updateOrderBookInState(state: TradingState, newOrderBook: OrderBook): TradingState {
    return {
      ...state,
      orderBook: newOrderBook,
    };
  }

  // Update OHLCV in state
  private updateOHLCVInState(state: TradingState, newOHLCV: OHLCV[]): TradingState {
    // Keep only the most recent OHLCV data
    const sortedOHLCV = newOHLCV.sort((a, b) => a[0] - b[0]);
    
    return {
      ...state,
      ohlcv: sortedOHLCV.slice(-100), // Keep last 100 candles
    };
  }

  // Validate order book data
  private validateOrderBook(orderBook: OrderBook): boolean {
    return !!(
      orderBook &&
      orderBook.bids &&
      orderBook.asks &&
      orderBook.bids.length > 0 &&
      orderBook.asks.length > 0 &&
      orderBook.bids[0][0] > 0 &&
      orderBook.asks[0][0] > 0
    );
  }

  // Validate trade data
  private validateTrade(trade: Trade): boolean {
    return !!(
      trade &&
      trade.amount > 0 &&
      trade.price > 0 &&
      trade.timestamp > 0 &&
      (trade.side === 'buy' || trade.side === 'sell')
    );
  }

  // Validate OHLCV data
  private validateOHLCV(ohlcv: OHLCV): boolean {
    return !!(
      ohlcv &&
      Array.isArray(ohlcv) &&
      ohlcv.length >= 6 &&
      ohlcv[0] > 0 && // timestamp
      ohlcv[1] > 0 && // open
      ohlcv[2] > 0 && // high
      ohlcv[3] > 0 && // low
      ohlcv[4] > 0 && // close
      ohlcv[5] >= 0   // volume
    );
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const phemexHealth = await this.phemexService.healthCheck();
      const dataFreshness = Date.now() - this.lastUpdateTimestamp < 60000; // Data within last minute
      
      return this.isActive && phemexHealth && dataFreshness;
    } catch (error) {
      logger.error('DataCollectionAgent health check failed:', error);
      return false;
    }
  }

  // Get agent status
  getStatus() {
    return {
      isActive: this.isActive,
      lastUpdate: this.lastUpdateTimestamp,
      subscriptions: this.phemexService.activeSubscriptions,
      connected: this.phemexService.connected,
    };
  }

  // Stop the agent
  async stop(): Promise<void> {
    try {
      this.isActive = false;
      await this.phemexService.disconnect();
      this.removeAllListeners();
      
      logger.info('DataCollectionAgent stopped');
    } catch (error) {
      logger.error('Error stopping DataCollectionAgent:', error);
    }
  }
}
