# Deployment Guide

This guide covers deploying the Multi-AI Agent Trading System in various environments.

## 🚀 Quick Start

### 1. Prerequisites
- Node.js 18+ installed
- Git installed
- API keys for all required services

### 2. Installation
```bash
# Clone the repository
git clone <repository-url>
cd multi-ai-agent-trading-system

# Install dependencies and build
npm run setup

# Configure environment
cp .env.example .env
# Edit .env with your API keys
```

### 3. Configuration
Edit `.env` file with your API credentials:

```env
# Required API Keys
XAI_API_KEY="your_grok_api_key_here"
PHEMEX_API_KEY="your_phemex_api_key_here"
PHEMEX_SECRET="your_phemex_secret_here"
TAAPI_API_KEY="your_taapi_api_key_here"
DISCORD_BOT_TOKEN="your_discord_bot_token_here"
DISCORD_CHANNEL_ID="your_discord_channel_id_here"

# Trading Configuration
ENABLE_LIVE_TRADING="false"  # Set to "true" for live trading
```

### 4. Testing
```bash
# Run tests
npm test

# Check code quality
npm run check
```

### 5. Start the System
```bash
# Interactive startup (recommended)
npm start

# Direct startup (for production)
npm run start:direct
```

## 🔧 API Key Setup

### Phemex Exchange
1. Create account at [Phemex](https://phemex.com)
2. Go to API Management
3. Create new API key with trading permissions
4. Note: Use testnet for initial testing

### xAI (Grok 3)
1. Sign up at [xAI](https://x.ai)
2. Generate API key
3. Ensure sufficient credits for API calls

### TAAPI.io
1. Register at [TAAPI.io](https://taapi.io)
2. Get free or paid API key
3. Note rate limits for your plan

### Discord Bot
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create new application
3. Create bot and get token
4. Invite bot to your server with message permissions
5. Get channel ID where notifications will be sent

## 🖥️ Local Development

### Development Mode
```bash
# Start with hot reload
npm run dev

# Watch mode for TypeScript compilation
npm run watch

# Development with file watching
npm run dev:watch
```

### Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm test -- --coverage
```

### Code Quality
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

## 🐳 Docker Deployment

### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Create logs directory
RUN mkdir -p logs

# Expose port (if needed)
EXPOSE 3000

# Start the application
CMD ["npm", "run", "start:direct"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  trading-system:
    build: .
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### Build and Run
```bash
# Build Docker image
docker build -t trading-system .

# Run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f
```

## ☁️ Cloud Deployment

### AWS EC2
1. Launch EC2 instance (t3.medium recommended)
2. Install Node.js and PM2
3. Clone repository and configure
4. Use PM2 for process management

```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start dist/main.js --name "trading-system"

# Save PM2 configuration
pm2 save
pm2 startup
```

### Google Cloud Platform
1. Create Compute Engine instance
2. Set up firewall rules if needed
3. Deploy using similar steps as EC2

### DigitalOcean
1. Create droplet with Node.js
2. Configure domain and SSL if needed
3. Use systemd or PM2 for process management

## 🔒 Security Considerations

### API Key Security
- Never commit API keys to version control
- Use environment variables or secure vaults
- Rotate keys regularly
- Use minimum required permissions

### Network Security
- Use HTTPS for all API communications
- Consider VPN for sensitive operations
- Implement IP whitelisting where possible

### System Security
- Keep system and dependencies updated
- Use non-root user for running the application
- Monitor system logs for suspicious activity
- Implement proper backup strategies

## 📊 Monitoring and Logging

### Log Management
```bash
# View real-time logs
tail -f logs/combined.log

# View error logs only
tail -f logs/error.log

# Rotate logs (add to crontab)
0 0 * * * /usr/bin/logrotate /path/to/logrotate.conf
```

### Health Monitoring
The system includes built-in health checks:
- API connectivity monitoring
- WebSocket connection status
- Trading performance metrics
- Error rate tracking

### Alerting
Set up alerts for:
- System errors
- API failures
- Unusual trading activity
- Performance degradation

## 🔄 Maintenance

### Regular Tasks
- Monitor system performance
- Review trading logs
- Update dependencies
- Backup configuration and logs
- Test disaster recovery procedures

### Updates
```bash
# Pull latest changes
git pull origin main

# Install new dependencies
npm install

# Rebuild application
npm run build

# Restart system
pm2 restart trading-system
```

### Backup
```bash
# Backup configuration
cp .env .env.backup.$(date +%Y%m%d)

# Backup logs
tar -czf logs-backup-$(date +%Y%m%d).tar.gz logs/

# Backup database (if applicable)
# Add database backup commands here
```

## 🚨 Troubleshooting

### Common Issues

#### API Connection Failures
- Check API keys and permissions
- Verify network connectivity
- Check rate limits
- Review firewall settings

#### WebSocket Disconnections
- Monitor network stability
- Check exchange status
- Review reconnection logs
- Verify API permissions

#### Trading Execution Issues
- Confirm live trading is enabled
- Check account balance
- Verify symbol configuration
- Review order history

#### Discord Notifications Not Working
- Verify bot token and permissions
- Check channel ID
- Ensure bot is in the server
- Review Discord API status

### Log Analysis
```bash
# Search for errors
grep -i error logs/combined.log

# Check API failures
grep -i "api.*fail" logs/combined.log

# Monitor trading activity
grep -i "trade.*executed" logs/combined.log
```

### Performance Optimization
- Monitor memory usage
- Check CPU utilization
- Optimize database queries (if applicable)
- Review network latency
- Consider scaling strategies

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review system logs
3. Verify configuration
4. Check API service status
5. Create GitHub issue with details

## 🔄 Disaster Recovery

### Backup Strategy
- Daily configuration backups
- Log file rotation and archival
- Database backups (if applicable)
- Code repository backups

### Recovery Procedures
1. Restore from backup
2. Verify configuration
3. Test API connections
4. Validate trading functionality
5. Monitor system health

### Failover Planning
- Identify critical dependencies
- Plan for API service outages
- Prepare alternative communication channels
- Document recovery procedures
