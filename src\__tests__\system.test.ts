import { TradingState, TradingConfig, APIConfig } from '../types';
import { calculateCVD, detectOrderBookImbalance, generateTradingSignal } from '../utils';

describe('Trading System Tests', () => {
  const mockTradingConfig: TradingConfig = {
    symbol: 'BTC/USD:USD',
    leverage: 10,
    riskPercentage: 1.5,
    timeframe: '15m',
    enableLiveTrading: false,
    cvdThreshold: 1000,
    imbalanceThreshold: 0.05,
    momentumThreshold: 50,
    cvdDecayFactor: 0.001,
    orderBookLevels: 20,
    emaPeriodsForCVD: 5,
    graphExecutionInterval: 1000,
    maxRecentTrades: 1000,
    maxCVDHistory: 100,
  };

  describe('CVD Calculation', () => {
    it('should calculate CVD correctly with buy trades', () => {
      const trades = [
        { side: 'buy', amount: 100, timestamp: Date.now(), price: 50000 },
        { side: 'buy', amount: 200, timestamp: Date.now(), price: 50100 },
      ];

      const cvd = calculateCVD(trades as any);
      expect(cvd).toBeGreaterThan(0);
    });

    it('should calculate CVD correctly with sell trades', () => {
      const trades = [
        { side: 'sell', amount: 100, timestamp: Date.now(), price: 50000 },
        { side: 'sell', amount: 200, timestamp: Date.now(), price: 49900 },
      ];

      const cvd = calculateCVD(trades as any);
      expect(cvd).toBeLessThan(0);
    });

    it('should handle empty trades array', () => {
      const cvd = calculateCVD([]);
      expect(cvd).toBe(0);
    });
  });

  describe('Order Book Imbalance Detection', () => {
    it('should detect bid imbalance', () => {
      const orderBook = {
        bids: [
          [50000, 10],
          [49990, 20],
          [49980, 30],
        ],
        asks: [
          [50010, 5],
          [50020, 10],
          [50030, 15],
        ],
        timestamp: Date.now(),
        datetime: new Date().toISOString(),
        nonce: undefined,
      };

      const vwapData = detectOrderBookImbalance(orderBook as any, 3);
      expect(vwapData.imbalanceRatio).toBeGreaterThan(0);
    });

    it('should detect ask imbalance', () => {
      const orderBook = {
        bids: [
          [50000, 5],
          [49990, 10],
          [49980, 15],
        ],
        asks: [
          [50010, 10],
          [50020, 20],
          [50030, 30],
        ],
        timestamp: Date.now(),
        datetime: new Date().toISOString(),
        nonce: undefined,
      };

      const vwapData = detectOrderBookImbalance(orderBook as any, 3);
      expect(vwapData.imbalanceRatio).toBeLessThan(0);
    });
  });

  describe('Signal Generation', () => {
    it('should generate buy signal with positive conditions', () => {
      const signal = generateTradingSignal(
        1500, // CVD above threshold
        0.06, // Imbalance above threshold
        60,   // Momentum above threshold
        50000, // Price
        {
          cvdThreshold: 1000,
          imbalanceThreshold: 0.05,
          momentumThreshold: 50,
        }
      );

      expect(signal.type).toBe('buy');
      expect(signal.strength).toBeGreaterThan(60);
      expect(signal.confidence).toBeGreaterThan(0.6);
    });

    it('should generate sell signal with negative conditions', () => {
      const signal = generateTradingSignal(
        -1500, // CVD below threshold
        -0.06, // Imbalance below threshold
        -60,   // Momentum below threshold
        50000, // Price
        {
          cvdThreshold: 1000,
          imbalanceThreshold: 0.05,
          momentumThreshold: 50,
        }
      );

      expect(signal.type).toBe('sell');
      expect(signal.strength).toBeGreaterThan(60);
      expect(signal.confidence).toBeGreaterThan(0.6);
    });

    it('should generate hold signal with weak conditions', () => {
      const signal = generateTradingSignal(
        500,  // CVD below threshold
        0.02, // Imbalance below threshold
        20,   // Momentum below threshold
        50000, // Price
        {
          cvdThreshold: 1000,
          imbalanceThreshold: 0.05,
          momentumThreshold: 50,
        }
      );

      expect(signal.type).toBe('hold');
      expect(signal.strength).toBeLessThan(60);
    });
  });

  describe('Trading State', () => {
    it('should create valid initial state', () => {
      const initialState: TradingState = {
        recentTrades: [],
        orderBook: null,
        ohlcv: [],
        cvdHistory: [],
        ema: 0,
        imbalance: 0,
        vwap: {
          bidVWAP: 0,
          askVWAP: 0,
          imbalanceRatio: 0,
          timestamp: 0,
        },
        signals: [],
        lastSignalTimestamp: 0,
        decision: null,
        activePositions: [],
        tradeEvents: [],
        lastUpdateTimestamp: 0,
        connectionStatus: {
          phemex: false,
          discord: false,
          taapi: false,
          grok: false,
          lastHealthCheck: 0,
        },
        errorCount: 0,
      };

      expect(initialState).toBeDefined();
      expect(initialState.recentTrades).toEqual([]);
      expect(initialState.orderBook).toBeNull();
      expect(initialState.errorCount).toBe(0);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate trading configuration', () => {
      expect(mockTradingConfig.leverage).toBeGreaterThan(0);
      expect(mockTradingConfig.leverage).toBeLessThanOrEqual(100);
      expect(mockTradingConfig.riskPercentage).toBeGreaterThan(0);
      expect(mockTradingConfig.riskPercentage).toBeLessThanOrEqual(10);
      expect(mockTradingConfig.cvdThreshold).toBeGreaterThan(0);
      expect(mockTradingConfig.imbalanceThreshold).toBeGreaterThan(0);
      expect(mockTradingConfig.imbalanceThreshold).toBeLessThan(1);
    });

    it('should have valid symbol format', () => {
      expect(mockTradingConfig.symbol).toMatch(/^[A-Z]{3,5}\/[A-Z]{3,5}:[A-Z]{3,5}$/);
    });

    it('should have valid timeframe', () => {
      const validTimeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d'];
      expect(validTimeframes).toContain(mockTradingConfig.timeframe);
    });
  });
});

describe('Integration Tests', () => {
  it('should handle complete data flow', () => {
    // Mock data flow through the system
    const trades = [
      { side: 'buy', amount: 100, timestamp: Date.now(), price: 50000 },
      { side: 'sell', amount: 50, timestamp: Date.now(), price: 50100 },
    ];

    const orderBook = {
      bids: [[50000, 10], [49990, 20]],
      asks: [[50010, 5], [50020, 10]],
      timestamp: Date.now(),
      datetime: new Date().toISOString(),
      nonce: undefined,
    };

    // Calculate indicators
    const cvd = calculateCVD(trades as any);
    const vwapData = detectOrderBookImbalance(orderBook as any, 2);

    // Generate signal
    const signal = generateTradingSignal(
      cvd,
      vwapData.imbalanceRatio,
      10, // momentum
      50000,
      {
        cvdThreshold: 1000,
        imbalanceThreshold: 0.05,
        momentumThreshold: 50,
      }
    );

    expect(signal).toBeDefined();
    expect(signal.type).toMatch(/^(buy|sell|hold)$/);
    expect(signal.timestamp).toBeGreaterThan(0);
    expect(signal.metadata.cvd).toBe(cvd);
    expect(signal.metadata.imbalance).toBe(vwapData.imbalanceRatio);
  });
});
