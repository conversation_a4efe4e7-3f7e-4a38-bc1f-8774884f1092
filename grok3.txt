With your xAI API key exported as an environment variable, you're ready to make your first API request.

Let's test out the API using 
curl
. Paste the following directly into your terminal.

bash


curl https://api.x.ai/v1/chat/completions \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $XAI_API_KEY" \
-d '{
    "messages": [
        {
            "role": "system",
            "content": "You are <PERSON><PERSON>, a highly intelligent, helpful AI assistant."
        },
        {
            "role": "user",
            "content": "What is the meaning of life, the universe, and everything?"
        }
    ],
    "model": "grok-3-beta",
    "stream": false,
    "temperature": 0
}'
#Step 4: Make a request from Python or Javascript
Our API is fully compatible with the OpenAI and Anthropic SDKs. For example, we can make the same request from Python or Javascript like so:


javascript


// In your terminal, first run:
// npm install openai

import OpenAI from "openai";

const client = new OpenAI({
    apiKey: "your_api_key",
    baseURL: "https://api.x.ai/v1",
});

const completion = await client.chat.completions.create({
    model: "grok-3-beta",
    messages: [
        {
            role: "system",
            content:
                "You are <PERSON><PERSON>, a highly intelligent, helpful AI assistant.",
        },
        {
            role: "user",
            content:
                "What is the meaning of life, the universe, and everything?",
        },
    ],
});

console.log(completion.choices[0].message.content);
Certain models (such as 
grok-3-beta
, 
grok-3-mini-beta
, and 
grok-2-vision-1212
) also support Structured Outputs, which allows you to enforce a schema for the LLM output.

For an in-depth guide about using Grok for text responses, check out our Chat Guide.

#Step 5: Use Grok to analyze images
Certain grok models can accept both text AND images as an input. For example:


javascript


import OpenAI from "openai";
        
const client = new OpenAI({
    apiKey: process.env.XAI_API_KEY,
    baseURL: "https://api.x.ai/v1",
});

const image_url =
    "https://science.nasa.gov/wp-content/uploads/2023/09/web-first-images-release.png";

const completion = await client.chat.completions.create({
    model: "grok-2-vision-latest",
    messages: [
        {
            role: "user",
            content: [
                {
                    type: "image_url",
                    image_url: {
                        url: image_url,
                        detail: "high",
                    },
                },
                {
                    type: "text",
                    text: "What's in this image?",
                },
            ],
        },
    ],
});

console.log(completion.choices[0].message.content);
And voila! Grok will tell you exactly what's in the image:

This image is a photograph of a region in space, specifically a part of the Carina Nebula, captured by the James Webb Space Telescope. It showcases a stunning view of interstellar gas and dust, illuminated by young, hot stars. The bright points of light are stars, and the colorful clouds are composed of various gases and dust particles. The image highlights the intricate details and beauty of star formation within a nebula.

To learn how to use Grok vision for more advanced use cases, check out our Image Understanding Guide.

#Monitoring usage
As you use your API key, you will be charged for the number of tokens used. For an overview, you can monitor your usage on the xAI Console Usage Page.

If you want a more granular, per request usage tracking, the API response includes a usage object that provides detail on prompt (input) and completion (output) token usage.

json


"usage": {
  "prompt_tokens": 41,
  "completion_tokens": 87,
  "total_tokens": 128,
  "prompt_tokens_details": {
    "text_tokens": 41,
    "audio_tokens": 0,
    "image_tokens": 0,
    "cached_tokens": 0
  }
}
If you send requests too frequently or with long prompts, you might run into rate limits and get an error response. For more information, read Consumption and Rate Limits.

#Next steps
Now you have learned the basics of making an inference on xAI API. Check out Models page to start building with one of our latest models.





Chat
Text in, text out. Chat is the most popular feature on the xAI API, and can be used for anything from summarizing articles, generating creative writing, answering questions, providing customer support, to assisting with coding tasks.

#Prerequisites
xAI Account: You need an xAI account to access the API.
API Key: Ensure that your API key has access to the chat endpoint and the chat model is enabled.
If you don't have these and are unsure of how to create one, follow the Hitchhiker's Guide to Grok.

You can create an API key on the xAI Console API Keys Page.

Set your API key in your environment:

bash


export XAI_API_KEY="your_api_key"
#A Basic Chat Completions Example
You can also stream the response, which is covered in Streaming Response.

The user sends a request to the xAI API endpoint. The API processes this and returns a complete response.


javascript


import OpenAI from "openai";

const client = new OpenAI({
    apiKey: "<api key>",
    baseURL: "https://api.x.ai/v1",
});

const completion = await client.chat.completions.create({
    model: "grok-3-latest",
    messages: [
        {
            role: "system",
            content: "You are Grok, a chatbot inspired by the Hitchhiker's Guide to the Galaxy."
        },
        {
            role: "user",
            content: "What is the meaning of life, the universe, and everything?"
        },
    ],
});
console.log(completion.choices[0].message);
Response:


javascript


{
  role: 'assistant',
  content: `Ah, the ultimate question! According to Douglas Adams' "The Hitchhiker's Guide to the Galaxy," the answer to the ultimate question of life, the universe, and everything is **42**. However, the guide also notes that the actual question to which this is the answer is still unknown. Isn't that delightfully perplexing? Now, if you'll excuse me, I'll just go ponder the intricacies of existence.`
  refusal: null
}
#Conversations
The xAI API is stateless and does not process new request with the context of your previous request history.

However, you can provide previous chat generation prompts and results to a new chat generation request to let the model process your new request with the context in mind.

An example message:

json


{
  "role": "system",
  "content": [{ "type": "text", "text": "You are a helpful and funny assistant."}]
}
{
  "role": "user",
  "content": [{ "type": "text", "text": "Why don't eggs tell jokes?" }]
},
{
  "role": "assistant",
  "content": [{ "type": "text", "text": "They'd crack up!" }]
},
{
  "role": "user",
  "content": [{"type": "text", "text": "Can you explain the joke?"}],
}
By specifying roles, you can change how the the model ingest the content. The 
system
 role content should define, in an instructive tone, the way the model should respond to user request. The 
user
 role content is usually used for user request or data sent to the model. The 
assistant
 role content is usually either in the model's response, or when sent within the prompt, indicating the model's response as part of conversation history.

This strategy to send 
assistant
 role content can be used within function calling, in which the model response will invoke a tool call, the user's program responds to the tool call and continues the conversation by appending tool call result to the message. For more details, check out our guide on Function Calling.

#Message role order flexibility
Unlike some models from other providers, one of the unique aspects of xAI API is its flexibility with message roles:

No Order Limitation: You can mix 
system
, 
user
, or 
assistant
 roles in any sequence for your conversation context.
Example 1 - Multiple System Messages:

json


[
{"role": "system", "content": "..."},
{"role": "system", "content": "..."},
{"role": "user", "content": "..."},
{"role": "user", "content": "..."}
]
The model takes multiple system

Example 2 - User Messages First:

json


{"role": "user", "content": "..."},
{"role": "user", "content": "..."},
{"role": "system", "content": "..


Grok 3 Mini is a lightweight, smaller thinking model. Unlike traditional models that generate answers immediately, Grok 3 Mini thinks before responding. It’s ideal for reasoning-heavy tasks that don’t demand extensive domain knowledge, and shines in math-specific and quantitative use cases, such as solving challenging puzzles or math problems.

Reasoning is only supported by 
grok-3-mini-beta
 and 
grok-3-mini-fast-beta
.
The Grok 3 models 
grok-3-beta
 and 
grok-3-fast-beta
 do not support reasoning.

#Key Features
Think Before Responding: Thinks through problems step-by-step before delivering an answer.
Math & Quantitative Strength: Excels at numerical challenges and logic puzzles.
Reasoning Trace: The model's thoughts are available via the 
reasoning_content
 field in the response completion object (see example below).
You can access the model's raw thinking trace via the 
message.reasoning_content
 of the chat completion response.

#Control how hard the model thinks
The 
reasoning_effort
 parameter controls how much time the model spends thinking before responding. It must be set to one of these values:

low
: Minimal thinking time, using fewer tokens for quick responses.
high
: Maximum thinking time, leveraging more tokens for complex problems.
Choosing the right level depends on your task: use 
low
 for simple queries that should complete quickly, and 
high
 for harder problems where response latency is less important.

#Usage Example
Here’s a simple example using Grok 3 Mini to multiply 101 by 3. Notice that we can access both the reasoning content and final response.


javascript


import OpenAI from "openai";

const client = new OpenAI({
    apiKey: "<api key>",
    baseURL: "https://api.x.ai/v1",
});

const completion = await client.chat.completions.create({
    model: "grok-3-mini-beta",
    messages: [
      {
          "role": "system",
          "content": "You are a highly intelligent AI assistant.",
      },
      {
          "role": "user",
          "content": "What is 101*3?",
      },
    ],
});

console.log("Reasoning Content:", completion.choices[0].message.reasoning_content);

console.log("\nFinal Response:", completion.choices[0].message.content);

console.log("\nNumber of completion tokens (input):", completion.usage.completion_tokens);

console.log("\nNumber of reasoning tokens (input):", completion.usage.completion_tokens_details.reasoning_tokens);
#Sample Output
output


Reasoning Content:
Let me calculate 101 multiplied by 3:
101 * 3 = 303.
I can double-check that: 100 * 3 is 300, and 1 * 3 is 3, so 300 + 3 = 303. Yes, that's correct.

Final Response:
The result of 101 multiplied by 3 is 303.

Number of completion tokens (input):
14

Number of reasoning tokens (input):
310
#When to Use Reasoning
Use 
grok-3-mini-beta
 or 
grok-3-mini-fast-beta
: For tasks that can benefit from logical reasoning (such as meeting scheduling or math problems). Also great for tasks that don't require deep domain knowledge about a specific subject (eg basic customer support bot).
Use 
grok-3-beta
 or 
grok-3-fast-beta
: For queries requiring deep domain expertise or world knowledge (eg healthcare, legal, finance).



Streaming Response
Streaming outputs is supported by all models with text output capability (Chat, Image Understanding, etc.). It is not supported by models with image output capability (Image Generation).

Streaming outputs uses Server-Sent Events (SSE) that let the server send back the delta of content in event streams.

Streaming responses are beneficial for providing real-time feedback, enhancing user interaction by allowing text to be displayed as it's generated.

To enable streaming, you must set 
"stream": true
 in your request:


javascript


import OpenAI from "openai";
const openai = new OpenAI({
  apiKey: "<api key>",
  baseURL: "https://api.x.ai/v1",
});

const stream = await openai.chat.completions.create({
  model: "grok-3-latest",
  messages: [
    { role: "system", content: "You are Grok, a chatbot inspired by the Hitchhiker's Guide to the Galaxy." },
    {
      role: "user",
      content: "What is the meaning of life, the universe, and everything?",
    }
  ],
  stream: true
});

for await (const chunk of stream) {
    console.log(chunk.choices[0].delta.content);
}
You'll get the event streams like these:

bash


data: {
"id":"<completion_id>","object":"chat.completion.chunk","created":<creation_time>,
"model":"grok-3-beta",
"choices":[{"index":0,"delta":{"content":"Ah","role":"assistant"}}],
"usage":{"prompt_tokens":41,"completion_tokens":1,"total_tokens":42,
"prompt_tokens_details":{"text_tokens":41,"audio_tokens":0,"image_tokens":0,"cached_tokens":0}},
"system_fingerprint":"fp_xxxxxxxxxx"}

data: {
"id":"<completion_id>","object":"chat.completion.chunk","created":<creation_time>,
"model":"grok-3-beta",
"choices":[{"index":0,"delta":{"content":",","role":"assistant"}}],
"usage":{"prompt_tokens":41,"completion_tokens":2,"total_tokens":43,
"prompt_tokens_details":{"text_tokens":41,"audio_tokens":0,"image_tokens":0,"cached_tokens":0}},
"system_fingerprint":"fp_xxxxxxxxxx"
}

data: [DONE]
It is recommended that you use a client SDK to parse the event stream.

Example streaming responses in Python/Javascript:

bash


Ah, the ultimate question! According to Douglas Adams, the answer is **42**. However, the trick lies in figuring out what the actual question is. If you're looking for a bit more context or a different perspective:

- **Philosophically**: The meaning of life might be to seek purpose, happiness, or to fulfill one's potential.
- **Biologically**: It could be about survival, reproduction, and passing on genes.
- **Existentially**: You create your own meaning through your experiences and choices.

But let's not forget, the journey to find this meaning might just be as important as the answer itself! Keep exploring, questioning, and enjoying the ride through the universe. And remember, don't panic!


Deferred Chat Completions
Deferred Chat Completions are currently available only via REST requests, not through any SDKs.
Deferred Chat Completions allow you to create a chat completion, get a 
response_id
, and retrieve the response at a later time. The result would be available to be requested exactly once within 24 hours, after which it would be discarded.

Deferred chat flow
After sending the request to the xAI API, the chat completion result will be available at 
https://api.x.ai/v1/chat/deferred-completion/{request_id}
. The response body will contain 
{'request_id': 'f15c114e-f47d-40ca-8d5c-8c23d656eeb6'}
, and the 
request_id
 value can be inserted into the 
deferred-completion
 endpoint path. Then, we send this GET request to retrieve the deferred completion result.

When the completion result is not ready, the request will return 
202 Accepted
 with an empty response body.

#Example
An example code is provided below, where we retry retrieving the result until it have been processed:


javascript


const axios = require('axios');
const retry = require('retry');

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${process.env.XAI_API_KEY}`
};

const payload = {
  messages: [
    { role: 'system', content: 'You are Zaphod Beeblebrox.' },
    { role: 'user', content: '126/3=?' }
  ],
  model: 'grok-3-latest',
  deferred: true
};

async function main() {
  const requestId = (await axios.post('https://api.x.ai/v1/chat/completions', payload, { headers })).data.request_id;
  console.log(`Request ID: ${requestId}`);

  const operation = retry.operation({
    minTimeout: 1000,
    maxTimeout: 60000,
    factor: 2
  });

  const completion = await new Promise((resolve, reject) => {
    operation.attempt(async () => {
      const res = await axios.get(`https://api.x.ai/v1/chat/deferred-completion/${requestId}`, { headers });
      if (res.status === 200) resolve(res.data);
      else if (res.status === 202) operation.retry(new Error('Not ready'));
      else reject(new Error(`${res.status}: ${res.statusText}`));
    });
  });

  console.log(JSON.stringify(completion, null, 4));
}

main().catch(console.error);
The response body will be the same as what you would expect with non-deferred chat completions:

json


{
    "id": "c0161816-8b53-4c28-bd2b-3877c6edb800",
    "object": "chat.completion",
    "created": 3141592653,
    "model": "grok-3-beta",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hey, don't ask me about math, I'm Zaphod Beeblebrox, not a calculator! But if you really need to know, it's 42, isn't it? Everything's 42!",
                "refusal": null
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 27,
        "completion_tokens": 48,
        "reasoning_tokens": 0,
        "total_tokens": 75,
        "prompt_tokens_details": {
            "text_tokens": 27,
            "audio_tokens": 0,
            "image_tokens": 0,
            "cached_tokens": 0
        }
    },
    "system_fingerprint": "fp_fe9e7ef66e"
}
For more details, refer to Chat completions and Get deferred chat completions in our REST API Reference.


Function calling
Connect the xAI models to external tools and systems to build AI assistants and various integrations.

#Introduction
Function calling enables language models to use external tools, which can intimately connect models to digital and physical worlds.

This is a powerful capability that can be used to enable a wide range of use cases.

Calling public APIs for actions ranging from looking up football game results to getting real-time satellite positioning data
Analyzing internal databases
Browsing web pages
Executing code
Interacting with the physical world (e.g. booking a flight ticket, opening your tesla car door, controlling robot arms)
#Walkthrough
The request/response flow for function calling can be demonstrated in the following illustration.

Function call request/response flow example
You can think of it as the LLM initiating RPCs (Remote Procedure Calls) to user system. From the LLM's perspective, the "2. Response" is an RPC request from LLM to user system, and the "3. Request" is an RPC response with information that LLM needs.

One simple example of a local computer/server, where the computer/server determines if the response from Grok contains a 
tool_call
, and calls the locally-defined functions to perform user-defined actions:

Local computer/server setup for function calling
The whole process looks like this in pseudocode:

pseudocode


// ... Define tool calls and their names

messages = []

/* Step 1: Send a new user request */

messages += {<new user request message>}
response = send_request_to_grok(message)

messages += response.choices[0].message  // Append assistant response

while (true) {
    /* Step 2: Run tool call and add tool call result to messages */ 
    if (response contains tool_call) {
        // Grok asks for tool call

        for (tool in tool_calls) {
            tool_call_result = tool(arguments provided in response) // Perform tool call
            messages += tool_call_result  // Add result to message
        }
    }

    read(user_request)

    if (user_request) {
        messages += {<new user request message>}
    }

    /* Step 3: Send request with tool call result to Grok*/
    response = send_request_to_grok(message)

    print(response)
}

We will demonstrate the function calling in the following Python script. First, let's create an API client:

python


import os
import json
from openai import OpenAI

XAI_API_KEY = os.getenv("XAI_API_KEY")

client = OpenAI(
    api_key=XAI_API_KEY,
    base_url="https://api.x.ai/v1",
)
#Preparation - Define tool functions and function mapping
Define tool functions as callback functions to be called when model requests them in response.

Normally, these functions would either retrieve data from a database, or call another API endpoint, or perform some actions. For demonstration purposes, we hardcode to return 59° Fahrenheit/15° Celsius as the temperature, and 15,000 feet as the cloud ceiling.

The parameters definition will be sent in the initial request to Grok, so Grok knows what tools and parameters are available to be called.

To reduce human error, you can define the tools partially using Pydantic.

Function definition using Pydantic:

python


from pydantic import BaseModel, Field
from typing import Literal

# Defining functions and function arguments
class TemperatureRequest(BaseModel):
    location: str = Field(description="The city and state, e.g. San Francisco, CA")
    unit: Literal["celsius", "fahrenheit"] = Field(
        "fahrenheit", description="Temperature unit"
    )

class CeilingRequest(BaseModel):
    location: str = Field(description="The city and state, e.g. San Francisco, CA")

def get_current_temperature(**kwargs):
    request = TemperatureRequest(**kwargs)
    temperature: int
    if request.unit.lower() == "fahrenheit":
        temperature = 59
    elif request.unit.lower() == "celsius":
        temperature = 15
    else:
        raise ValueError("unit must be one of fahrenheit or celsius")
    return {
        "location": request.location,
        "temperature": temperature,
        "unit": request.unit.lower(),
    }

def get_current_ceiling(**kwargs):
    request = CeilingRequest(**kwargs)
    return {
        "location": request.location,
        "ceiling": 15000,
        "ceiling_type": "broken",
        "unit": "ft",
    }


# Generate the JSON schema
get_current_temperature_schema = TemperatureRequest.model_json_schema()
get_current_ceiling_schema = CeilingRequest.model_json_schema()

# Definition of parameters with Pydantic JSON schema
tools_definition = [
    {
        "type": "function",
        "function": {
            "name": "get_current_temperature",
            "description": "Get the current temperature in a given location",
            "parameters": get_current_temperature_schema,
        },
    },
    {
        "type": "function",
        "function": {
            "name": "get_current_ceiling",
            "description": "Get the current cloud ceiling in a given location",
            "parameters": get_current_ceiling_schema,
        },
    },
]
Function definition using raw dictionary:

python


# Defining functions
def get_current_temperature(location: str, unit: str = "fahrenheit"):
    temperature: int
    if unit.lower() == "fahrenheit":
        temperature = 59
    elif unit.lower() == "celsius":
        temperature = 15
    else:
        raise ValueError("unit must be one of fahrenheit or celsius")
    return {"location": location, "temperature": temperature, "unit": unit}


def get_current_ceiling(location: str):
    return {
        "location": location,
        "ceiling": 15000,
        "ceiling_type": "broken",
        "unit": "ft",
    }

tools_map = {
    "get_current_temperature": get_current_temperature,
    "get_current_ceiling": get_current_ceiling,
}

# Raw dictionary definition of parameters
tools_definition = [
    {
        "type": "function",
        "function": {
            "name": "get_current_temperature",
            "description": "Get the current temperature in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "default": "fahrenheit"
                    }
                },
                "required": ["location"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_current_ceiling",
            "description": "Get the current cloud ceiling in a given location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA"
                    }
                },
                "required": ["location"]
            }
        }
    }
]
Create a string -> function mapping, so we can call the function when model sends it's name. e.g.

python


tools_map = {
    "get_current_temperature": get_current_temperature,
    "get_current_ceiling": get_current_ceiling,
}
#1. Send initial message
With all the functions defined, it's time to send our API request to Grok!

Now before we send it over, let's look at how the generic request body for a new task looks like.

Here we assume a previous tool call has Note how the tool call is referenced three times:

By 
id
 and 
name
 in "Mesage History" assistant's first response
By 
tool_call_id
 in "Message History" tool's content
In the 
tools
 field of the request body
Function call new request body
Now we compose the request messages in the request body and send it over to Grok. Grok should return a response that asks us for a tool call.

python


messages = [{"role": "user", "content": "What's the temperature like in San Francisco?"}]
response = client.chat.completions.create(
    model="grok-3-latest",
    messages=messages,
    tools=tools_definition,  # The dictionary of our functions and their parameters
    tool_choice="auto",
)

# You can inspect the response which contains a tool call
print(response.choices[0].message)
#2. Run tool functions if Grok askes tool call and append function returns to message
We retrieve the tool function names and arguments that Grok wants to call, run the functions, and add the result to messages.

At this point, you can choose to only respond to tool call with results or add a new user message request.

The 
tool
 message would contain the following: 
{ "role": "tool", "content": <json string of tool function's returned object>, "tool_call_id": <tool_call.id included in the tool call response by Grok>}

The request body that we try to assemble and send back to Grok. Note it looks slightly different from the new task request body:

Request body after processing tool call
The corresponding code to append messages:

python


# Append assistant message including tool calls to messages
messages.append(response.choices[0].message)

# Check if there is any tool calls in response body
# You can also wrap this in a function to make the code cleaner

if response.choices[0].message.tool_calls:
    for tool_call in response.choices[0].message.tool_calls:

        # Get the tool function name and arguments Grok wants to call
        function_name = tool_call.function.name
        function_args = json.loads(tool_call.function.arguments)

        # Call one of the tool function defined earlier with arguments
        result = tools_map[function_name](**function_args)

        # Append the result from tool function call to the chat message history,
        # with "role": "tool"
        messages.append(
            {
                "role": "tool",
                "content": json.dumps(result),
                "tool_call_id": tool_call.id  # tool_call.id supplied in Grok's response
            }
        )
#3. Send the tool function returns back to the model to get the response
python


response = client.chat.completions.create(
    model="grok-3-latest",
    messages=messages,
    tools=tools_definition,
    tool_choice="auto"
)

print(response.choices[0].message.content)
#4. (Optional) Continue the conversation
You can continue the conversation following Step 2. Otherwise you can terminate.

#Function calling modes
By default, the model will automatically decide whether a function call is necessary and select which functions to call, as determined by the 
tool_choice: "auto"
 setting.

We offer three ways to customize the default behavior:

To force the model to always call one or more functions, you can set 
tool_choice: "required"
. The model will then always call function. Note this could force the model to hallucinate parameters.
To force the model to call a specific function, you can set 
tool_choice: {"type": "function", "function": {"name": "my_function"}}
.
To disable function calling and force the model to only generate a user-facing message, you can either provide no tools, or set 
tool_choice: "none"


Structured Outputs
Structured Outputs is a feature that lets the API return responses in a specific, organized format, like JSON or other schemas you define. Instead of getting free-form text, you receive data that's consistent and easy to parse.

Ideal for tasks like document parsing, entity extraction, or report generation, it lets you define schemas using tools like Pydantic or Zod to enforce data types, constraints, and structure.

When using structured outputs, the LLM's response is guaranteed to match your input schema.
#Supported models
Structured outputs is supported for the following models:

grok-3
grok-3-fast
grok-3-mini
grok-3-mini-fast
grok-2-vision-1212
grok-2-1212
 (deprecated)
#Example: Invoice Parsing
A common use case for Structured Outputs is parsing raw documents. For example, invoices contain structured data like vendor details, amounts, and dates, but extracting this data from raw text can be error-prone. Structured Outputs ensure the extracted data matches a predefined schema.

Let's say you want to extract the following data from an invoice:

Vendor name and address
Invoice number and date
Line items (description, quantity, price)
Total amount and currency
We'll use structured outputs to have Grok generate a strongly-typed JSON for this.

#Step 1: Defining the Schema
You can use Pydantic or Zod to define your schema.


javascript


import { z } from "zod";

const CurrencyEnum = z.enum(["USD", "EUR", "GBP"]);

const LineItemSchema = z.object({
  description: z.string().describe("Description of the item or service"),
  quantity: z.number().int().min(1).describe("Number of units"),
  unit_price: z.number().min(0).describe("Price per unit"),
});

const AddressSchema = z.object({
  street: z.string().describe("Street address"),
  city: z.string().describe("City"),
  postal_code: z.string().describe("Postal/ZIP code"),
  country: z.string().describe("Country"),
});

const InvoiceSchema = z.object({
  vendor_name: z.string().describe("Name of the vendor"),
  vendor_address: AddressSchema.describe("Vendor's address"),
  invoice_number: z.string().describe("Unique invoice identifier"),
  invoice_date: z.string().date().describe("Date the invoice was issued"),
  line_items: z.array(LineItemSchema).describe("List of purchased items/services"),
  total_amount: z.number().min(0).describe("Total amount due"),
  currency: CurrencyEnum.describe("Currency of the invoice"),
});
#Step 2: Prepare The Prompts
#System Prompt
The system prompt instructs the model to extract invoice data from text. Since the schema is defined separately, the prompt can focus on the task without explicitly specifying the required fields in the output JSON.

text


Given a raw invoice, carefully analyze the text and extract the relevant invoice data into JSON format.
#Example Invoice Text
text


Vendor: Acme Corp, 123 Main St, Springfield, IL 62704
Invoice Number: INV-2025-001
Date: 2025-02-10
Items:
- Widget A, 5 units, $10.00 each
- Widget B, 2 units, $15.00 each
Total: $80.00 USD
#Step 3: The Final Code
Use the structured outputs feature of the the SDK to parse the invoice.


javascript


import OpenAI from "openai";
import { zodResponseFormat } from "openai/helpers/zod";
import { z } from "zod";

const CurrencyEnum = z.enum(["USD", "EUR", "GBP"]);

const LineItemSchema = z.object({
  description: z.string().describe("Description of the item or service"),
  quantity: z.number().int().min(1).describe("Number of units"),
  unit_price: z.number().min(0).describe("Price per unit"),
});

const AddressSchema = z.object({
  street: z.string().describe("Street address"),
  city: z.string().describe("City"),
  postal_code: z.string().describe("Postal/ZIP code"),
  country: z.string().describe("Country"),
});

const InvoiceSchema = z.object({
  vendor_name: z.string().describe("Name of the vendor"),
  vendor_address: AddressSchema.describe("Vendor's address"),
  invoice_number: z.string().describe("Unique invoice identifier"),
  invoice_date: z.string().date().describe("Date the invoice was issued"),
  line_items: z.array(LineItemSchema).describe("List of purchased items/services"),
  total_amount: z.number().min(0).describe("Total amount due"),
  currency: CurrencyEnum.describe("Currency of the invoice"),
});

const client = new OpenAI({
  apiKey: "<api key>",
  baseURL: "https://api.x.ai/v1",
});

const completion = await client.beta.chat.completions.parse({
  model: "grok-3-latest",
  messages: [
    { role: "system", content: "Given a raw invoice, carefully analyze the text and extract the invoice data into JSON format." },
    { role: "user", content: `
      Vendor: Acme Corp, 123 Main St, Springfield, IL 62704
      Invoice Number: INV-2025-001
      Date: 2025-02-10
      Items:
      - Widget A, 5 units, $10.00 each
      - Widget B, 2 units, $15.00 each
      Total: $80.00 USD
    ` },
  ],
  response_format: zodResponseFormat(InvoiceSchema, "invoice"),
});

const invoice = completion.choices[0].message.parsed;
console.log(invoice);
#Step 4: Type-safe Output
The output will always be type-safe and respect the input schema.

json


{
  "vendor_name": "Acme Corp",
  "vendor_address": {
    "street": "123 Main St",
    "city": "Springfield",
    "postal_code": "62704",
    "country": "IL"
  },
  "invoice_number": "INV-2025-001",
  "invoice_date": "2025-02-10",
  "line_items": [
    {"description": "Widget A", "quantity": 5, "unit_price": 10.0},
    {"description": "Widget B", "quantity": 2, "unit_price": 15.0}
  ],
  "total_amount": 80.0,
  "currency": "USD"
}


Chat completions
/v1/chat/completions

Create a chat response from text/image chat prompts. This is the endpoint for making requests to chat and image understanding models.

Request Body
Expand All
Search parameters...
messages

array

required


A list of messages that make up the the chat conversation. Different models support different message types, such as image and text.

model

string

required

Model name for the model to use. Obtainable from https://console.x.ai/team/default/models or https://docs.x.ai/docs/models.

Show optional fields
Response Body
Expand All
Search parameters...
choices

array


A list of response choices from the model. The length corresponds to the 
n
 in request body (default to 1).

created

integer

The chat completion creation time in Unix timestamp.

id

string

A unique ID for the chat response.

model

string

Model ID used to create chat completion.

object

string

The object type, which is always 
"chat.response"
.

Show nullable fields
Example
Definition
Try It Out
POST

/v1/chat/completions


{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant that can answer questions and help with tasks."
    },
    {
      "role": "user",
      "content": "What is 101*3?"
    }
  ],
  "reasoning_effort": "low",
  "model": "grok-3-mini-fast-latest"
}
200

Response


{
  "id": "a733959b-03c4-4944-b53a-af900075ba57",
  "object": "chat.completion",
  "created": 1743770302,
  "model": "grok-3-mini-fast-beta",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "101 multiplied by 3 equals 303.",
        "reasoning_content": "First, the user asked: \"What is 101*3?\" This is a simple multiplication question.\n\nI need to calculate 101 multiplied by 3. Let me do that mentally: 101 times 3 is 303.\n\nTo double-check: 100 times 3 is 300, and 1 times 3 is 3, so 300 + 3 = 303. Yes, that's correct.\n\nAs a helpful assistant, I should respond clearly and directly. Since this is straightforward, I don't need to add extra fluff unless it's necessary.\n\nThe system prompt says: \"You are a helpful assistant that can answer questions and help with tasks.\" So, answering directly fits.\n\nI should ensure my response is polite and engaging, but keep it concise.\n\nPossible response: \"101 multiplied by 3 equals 303.\"\n\nI could make it a bit more conversational: \"Sure, let me calculate that for you. 101 times 3 is 303.\"\n\nSince the user might be testing basic math, I could explain briefly, but that might be overkill for such a simple operation.\n\nFinally, structure the response: Start with the answer, and if needed, add any follow-up.\n\nResponse: \"The result of 101 multiplied by 3 is 303.\"",
        "refusal": null
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 32,
    "completion_tokens": 10,
    "total_tokens": 299,
    "prompt_tokens_details": {
      "text_tokens": 32,
      "audio_tokens": 0,
      "image_tokens": 0,
      "cached_tokens": 0
    },
    "completion_tokens_details": {
      "reasoning_tokens": 257,
      "audio_tokens": 0,
      "accepted_prediction_tokens": 0,
      "rejected_prediction_tokens": 0
    }
  },
  "system_fingerprint": "fp_11dc627712"
}
200
400
422